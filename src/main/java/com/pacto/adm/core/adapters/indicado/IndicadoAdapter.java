package com.pacto.adm.core.adapters.indicado;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.adapters.ClienteAdapter;
import com.pacto.adm.core.adapters.empresa.EmpresaAdapter;
import com.pacto.adm.core.adapters.indicacao.IndicacaoAdapter;
import com.pacto.adm.core.adapters.objecao.ObjecaoAdapter;
import com.pacto.adm.core.dto.indicado.IndicadoDTO;
import com.pacto.adm.core.entities.indicado.Indicado;
import org.springframework.stereotype.Component;

@Component
public class IndicadoAdapter implements AdapterInterface<Indicado, IndicadoDTO> {

    private final ClienteAdapter clienteAdapter;
    private final EmpresaAdapter empresaAdapter;
    private final IndicacaoAdapter indicacaoAdapter;
    private final ObjecaoAdapter objecaoAdapter;

    public IndicadoAdapter(
            ClienteAdapter clienteAdapter, EmpresaAdapter empresaAdapter, IndicacaoAdapter indicacaoAdapter,
            ObjecaoAdapter objecaoAdapter
    ) {
        this.clienteAdapter = clienteAdapter;
        this.empresaAdapter = empresaAdapter;
        this.indicacaoAdapter = indicacaoAdapter;
        this.objecaoAdapter = objecaoAdapter;
    }

    @Override
    public IndicadoDTO toDto(Indicado indicado) {
        IndicadoDTO indicadoDTO = new IndicadoDTO();
        
        indicadoDTO.setCodigo(indicado.getCodigo());
        indicadoDTO.setCpf(indicado.getCpf());
        indicadoDTO.setDataLancamento(indicado.getDataLancamento());
        indicadoDTO.setEmail(indicado.getEmail());
        indicadoDTO.setLead(indicado.getLead());
        indicadoDTO.setNomeConsulta(indicado.getNomeConsulta());
        indicadoDTO.setNomeIndicado(indicado.getNomeIndicado());
        indicadoDTO.setOrigemSistema(indicado.getOrigemSistema());
        indicadoDTO.setTelefone(indicado.getTelefone());
        indicadoDTO.setTelefoneIndicado(indicado.getTelefoneIndicado());
        if (indicado.getCliente() != null) {
            indicadoDTO.setCliente(clienteAdapter.toDto(indicado.getCliente()));
        }
        if (indicado.getEmpresa() != null) {
            indicadoDTO.setEmpresa(empresaAdapter.toDto(indicado.getEmpresa()));
        }
        if (indicado.getIndicacao() != null) {
            indicadoDTO.setIndicacao(indicacaoAdapter.toDto(indicado.getIndicacao()));
        }
        if (indicado.getObjecao() != null) {
            indicadoDTO.setObjecao(objecaoAdapter.toDto(indicado.getObjecao()));
        }
        
        return indicadoDTO;
    }

    @Override
    public Indicado toEntity(IndicadoDTO indicadoDTO) {
        Indicado indicado = new Indicado();

        indicado.setCodigo(indicadoDTO.getCodigo());
        indicado.setCpf(indicadoDTO.getCpf());
        indicado.setDataLancamento(indicadoDTO.getDataLancamento());
        indicado.setEmail(indicadoDTO.getEmail());
        indicado.setLead(indicadoDTO.getLead());
        indicado.setNomeConsulta(indicadoDTO.getNomeConsulta());
        indicado.setNomeIndicado(indicadoDTO.getNomeIndicado());
        indicado.setOrigemSistema(indicadoDTO.getOrigemSistema());
        indicado.setTelefone(indicadoDTO.getTelefone());
        indicado.setTelefoneIndicado(indicadoDTO.getTelefoneIndicado());
        if (indicadoDTO.getCliente() != null) {
            indicado.setCliente(clienteAdapter.toEntity(indicadoDTO.getCliente()));
        }
        if (indicadoDTO.getEmpresa() != null) {
            indicado.setEmpresa(empresaAdapter.toEntity(indicadoDTO.getEmpresa()));
        }
        if (indicadoDTO.getIndicacao() != null) {
            indicado.setIndicacao(indicacaoAdapter.toEntity(indicadoDTO.getIndicacao()));
        }
        if (indicadoDTO.getObjecao() != null) {
            indicado.setObjecao(objecaoAdapter.toEntity(indicadoDTO.getObjecao()));
        }

        return indicado;
    }
}
