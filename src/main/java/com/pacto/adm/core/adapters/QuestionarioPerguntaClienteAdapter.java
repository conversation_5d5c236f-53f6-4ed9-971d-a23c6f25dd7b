package com.pacto.adm.core.adapters;

import com.pacto.adm.core.dto.QuestionarioPerguntaClienteDTO;
import com.pacto.adm.core.entities.QuestionarioPerguntaCliente;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class QuestionarioPerguntaClienteAdapter implements AdapterInterface<QuestionarioPerguntaCliente, QuestionarioPerguntaClienteDTO> {

    @Autowired
    PerguntaClienteAdapter perguntaClienteAdapter;

    @Override
    public QuestionarioPerguntaCliente toEntity(QuestionarioPerguntaClienteDTO questionarioPerguntaClienteDTO) {
        if(questionarioPerguntaClienteDTO != null) {
            QuestionarioPerguntaCliente questionarioPerguntaCliente = new QuestionarioPerguntaCliente();
            questionarioPerguntaCliente.setPerguntaCliente(perguntaClienteAdapter.toEntity(questionarioPerguntaClienteDTO.getPerguntaCliente()));
            questionarioPerguntaCliente.setQuestionarioCliente(questionarioPerguntaClienteDTO.getQuestionarioCliente());
            questionarioPerguntaCliente.setCodigo(questionarioPerguntaClienteDTO.getCodigo());

            return questionarioPerguntaCliente;
        }
        return null;
    }

    @Override
    public QuestionarioPerguntaClienteDTO toDto(QuestionarioPerguntaCliente questionarioCliente) {
        if (questionarioCliente != null) {
            QuestionarioPerguntaClienteDTO questionarioPerguntaClienteDTO = new QuestionarioPerguntaClienteDTO();
            questionarioPerguntaClienteDTO.setPerguntaCliente(perguntaClienteAdapter.toDto(questionarioCliente.getPerguntaCliente()));
            questionarioPerguntaClienteDTO.setQuestionarioCliente(questionarioCliente.getQuestionarioCliente());
            questionarioPerguntaClienteDTO.setCodigo(questionarioCliente.getCodigo());

            return questionarioPerguntaClienteDTO;
        }
        return null;
    }
}
