package com.pacto.adm.core.adapters;

import com.pacto.adm.core.dto.AfastamentoContratoDependenteDTO;
import com.pacto.adm.core.entities.contrato.AfastamentoContratoDependente;
import org.springframework.stereotype.Component;

@Component
public class AfastamentoContratoDependenteAdapter implements AdapterInterface<AfastamentoContratoDependente, AfastamentoContratoDependenteDTO> {

    private final ContratoDependenteAdapter contratoDependenteAdapter;
    private final JustificativaOperacaoAdapter justificativaOperacaoAdapter;

    public AfastamentoContratoDependenteAdapter(ContratoDependenteAdapter contratoDependenteAdapter,
                                                JustificativaOperacaoAdapter justificativaOperacaoAdapter) {
        this.contratoDependenteAdapter = contratoDependenteAdapter;
        this.justificativaOperacaoAdapter = justificativaOperacaoAdapter;
    }

    @Override
    public AfastamentoContratoDependente toEntity(AfastamentoContratoDependenteDTO dto) {
        AfastamentoContratoDependente obj = new AfastamentoContratoDependente();
        obj = toEntity(dto, obj);
        return obj;
    }

    @Override
    public AfastamentoContratoDependente toEntity(AfastamentoContratoDependenteDTO dto, AfastamentoContratoDependente obj) {
        if (dto == null) {
            return null;
        }
        obj.setCodigo(dto.getCodigo());
        obj.setInicioAfastamento(dto.getInicioAfastamento());
        obj.setFinalAfastamento(dto.getFinalAfastamento());
        obj.setDataRegistro(dto.getDataRegistro());
        obj.setNrDiasSomar(dto.getNrDiasSomar());
        obj.setObservacao(dto.getObservacao());
        obj.setTipoAfastamento(dto.getTipoAfastamento());

        if (dto.getContratoDependente() != null) {
            obj.setContratoDependente(contratoDependenteAdapter.toEntity(dto.getContratoDependente()));
        }
        if (dto.getJustificativaOperacao() != null) {
            obj.setJustificativaOperacao(justificativaOperacaoAdapter.toEntity(dto.getJustificativaOperacao()));
        }
        return obj;
    }

    @Override
    public AfastamentoContratoDependenteDTO toDto(AfastamentoContratoDependente obj) {
        AfastamentoContratoDependenteDTO dto = new AfastamentoContratoDependenteDTO();
        if (obj != null) {
            dto.setCodigo(obj.getCodigo());
            dto.setInicioAfastamento(obj.getInicioAfastamento());
            dto.setFinalAfastamento(obj.getFinalAfastamento());
            dto.setDataRegistro(obj.getDataRegistro());
            dto.setNrDiasSomar(obj.getNrDiasSomar());
            dto.setObservacao(obj.getObservacao());
            dto.setTipoAfastamento(obj.getTipoAfastamento());

            if (obj.getContratoDependente() != null) {
                dto.setContratoDependente(contratoDependenteAdapter.toDto(obj.getContratoDependente()));
            }
            if (obj.getJustificativaOperacao() != null) {
                dto.setJustificativaOperacao(justificativaOperacaoAdapter.toDto(obj.getJustificativaOperacao()));
            }
            return dto;
        }
        return null;
    }

}
