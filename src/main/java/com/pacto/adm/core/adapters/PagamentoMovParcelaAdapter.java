package com.pacto.adm.core.adapters;

import com.pacto.adm.core.adapters.empresa.EmpresaAdapter;
import com.pacto.adm.core.dto.MovPagamentoDTO;
import com.pacto.adm.core.dto.PagamentoMovParcelaDTO;
import com.pacto.adm.core.entities.MovPagamento;
import com.pacto.adm.core.entities.financeiro.FormaPagamento;
import com.pacto.adm.core.entities.financeiro.PagamentoMovParcela;
import org.springframework.stereotype.Component;

@Component
public class PagamentoMovParcelaAdapter implements AdapterInterface<PagamentoMovParcela, PagamentoMovParcelaDTO> {


    private ReciboPagamentoAdapter reciboPagamentoAdapter;
    private MovPagamentoAdapter movPagamentoAdapter;
    private MovParcelaAdapter movParcelaAdapter;

    public PagamentoMovParcelaAdapter(ReciboPagamentoAdapter reciboPagamentoAdapter, MovPagamentoAdapter movPagamentoAdapter, MovParcelaAdapter movParcelaAdapter) {
        this.reciboPagamentoAdapter = reciboPagamentoAdapter;
        this.movPagamentoAdapter = movPagamentoAdapter;
        this.movParcelaAdapter = movParcelaAdapter;
    }

    @Override
    public PagamentoMovParcelaDTO toDto(PagamentoMovParcela pagamentoMovParcela) {
        PagamentoMovParcelaDTO pagamentoMovParcelaDTO = new PagamentoMovParcelaDTO();
        pagamentoMovParcelaDTO.setValorpago(pagamentoMovParcela.getValorPago());
        pagamentoMovParcelaDTO.setRecibo(reciboPagamentoAdapter.toDto(pagamentoMovParcela.getRecibo()));
        pagamentoMovParcelaDTO.setPagamento(movPagamentoAdapter.toDto(pagamentoMovParcela.getMovPagamento()));
        pagamentoMovParcelaDTO.setParcela(movParcelaAdapter.toDto(pagamentoMovParcela.getParcela()));

        return pagamentoMovParcelaDTO;
    }
}
