package com.pacto.adm.core.adapters.empresa;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.adapters.integracoes.EmpresaConfigEstacionamentoAdapter;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.entities.empresa.Empresa;
import org.springframework.stereotype.Component;

@Component
public class EmpresaAdapter implements AdapterInterface<Empresa, EmpresaDTO> {

    private final EmpresaConfigEstacionamentoAdapter empresaConfigEstacionamentoAdapter;

    public EmpresaAdapter(EmpresaConfigEstacionamentoAdapter empresaConfigEstacionamentoAdapter) {
        this.empresaConfigEstacionamentoAdapter = empresaConfigEstacionamentoAdapter;
    }

    @Override
    public EmpresaDTO toDto(Empresa empresa) {
        if (empresa != null) {
            EmpresaDTO empresaDTO = new EmpresaDTO();
            empresaDTO.setCodigo(empresa.getCodigo());
            empresaDTO.setNome(empresa.getNome());
            empresaDTO.setUsarNfce(empresa.getUsarNfce());
            empresaDTO.setUsarNfse(empresa.getUsarNfse());
            empresaDTO.setTrabalharComPontuacao(empresa.getTrabalharComPontuacao());
            empresaDTO.setAtiva(empresa.getAtiva() != null && empresa.getAtiva());
            empresaDTO.setIntegracaoMyWellneHabilitada(empresa.isIntegracaoMyWellneHabilitada() != null && empresa.isIntegracaoMyWellneHabilitada());
            empresaDTO.setIntegracaoMyWellnessEnviarVinculos(empresa.isIntegracaoMyWellnessEnviarVinculos() != null && empresa.isIntegracaoMyWellnessEnviarVinculos());
            empresaDTO.setIntegracaoMyWellnessEnviarGrupos(empresa.isIntegracaoMyWellnessEnviarGrupos() != null && empresa.isIntegracaoMyWellnessEnviarGrupos());
            empresaDTO.setIntegracaoMyWellnessFacilityUrl(empresa.getIntegracaoMyWellnessFacilityUrl());
            empresaDTO.setIntegracaMyWellneApiKey(empresa.getIntegracaMyWellneApiKey());
            empresaDTO.setIntegracaoMyWellnessUser(empresa.getIntegracaoMyWellnessUser());
            empresaDTO.setIntegracaoMyWellnessPassword(empresa.getIntegracaoMyWellnessPassword());
            empresaDTO.setNrDiasVigenciaMyWellnessGymPass(empresa.getNrDiasVigenciaMyWellnessGymPass());
            empresaDTO.setTipoVigenciaMyWellnessGympass(empresa.getTipoVigenciaMyWellnessGympass());
            empresaDTO.setIntegracaoMentorWebHabilitada(empresa.isIntegracaoMentorWebHabilitada() != null && empresa.isIntegracaoMentorWebHabilitada());
            empresaDTO.setIntegracaoMentorWebUrl(empresa.getIntegracaoMentorWebUrl());
            empresaDTO.setIntegracaoMentorWebServico(empresa.getIntegracaoMentorWebServico());
            empresaDTO.setIntegracaoMentorWebUser(empresa.getIntegracaoMentorWebUser());
            empresaDTO.setIntegracaoMentorWebPassword(empresa.getIntegracaoMentorWebPassword());
            empresaDTO.setUtilizaSistemaEstacionamento(empresa.isUtilizaSistemaEstacionamento() != null && empresa.isUtilizaSistemaEstacionamento());
            if (empresa.getEmpresaConfigEstacionamento() != null) {
                empresaDTO.setEmpresaConfigEstacionamento(empresaConfigEstacionamentoAdapter.toDto(empresa.getEmpresaConfigEstacionamento()));
                empresaDTO.getEmpresaConfigEstacionamento().setEmpresa(empresa.getCodigo());
            }
            empresaDTO.setArredondamento(empresa.getArredondamento());
            empresaDTO.setUsarParceiroFidelidade(empresa.isUsarParceiroFidelidade() != null && empresa.isUsarParceiroFidelidade());
            empresaDTO.setNotificarWebhook(empresa.isNotificarWebhook());
            empresaDTO.setUrlWebhookNotificar(empresa.getUrlWebhookNotificar());
            empresaDTO.setIntegracaoAmigoFitHabilitada(empresa.isIntegracaoAmigoFitHabilitada() != null && empresa.isIntegracaoAmigoFitHabilitada());
            empresaDTO.setNomeUsuarioAmigoFit(empresa.getNomeUsuarioAmigoFit());
            empresaDTO.setSenhaUsuarioAmigoFit(empresa.getSenhaUsuarioAmigoFit());
            empresaDTO.setCpfCodigoInternoWeHelp(empresa.isCpfCodigoInternoWeHelp());
            empresaDTO.setTokenBuzzLead(empresa.getTokenBuzzLead());
            empresaDTO.setTokenSMS(empresa.getTokenSMS());
            empresaDTO.setTokenSMSShortCode(empresa.getTokenSMSShortCode());
            empresaDTO.setIntegracaoF360RelFatHabilitada(empresa.isIntegracaoF360RelFatHabilitada() != null && empresa.isIntegracaoF360RelFatHabilitada());
            empresaDTO.setIntegracaoF360FtpServer(empresa.getIntegracaoF360FtpServer());
            empresaDTO.setIntegracaoF360FtpPort(empresa.getIntegracaoF360FtpPort());
            empresaDTO.setIntegracaoF360User(empresa.getIntegracaoF360User());
            empresaDTO.setIntegracaoF360Password(null);
            empresaDTO.setIntegracaoF360Dir(empresa.getIntegracaoF360Dir());
            empresaDTO.setIntegracaoF360Quinzenal(empresa.isIntegracaoF360Quinzenal() != null && empresa.isIntegracaoF360Quinzenal());
            empresaDTO.setUsarConciliadora(empresa.isUsarConciliadora() != null && empresa.isUsarConciliadora());
            empresaDTO.setEmpresaConciliadora(empresa.getEmpresaConciliadora());
            empresaDTO.setSenhaConciliadora(empresa.getSenhaConciliadora());
            empresaDTO.setCodigoGymPass(empresa.getCodigoGymPass());
            empresaDTO.setTokenApiGymPass(empresa.getTokenApiGymPass());
            empresaDTO.setIntegracaoSpiviHabilitada(empresa.isIntegracaoSpiviHabilitada() != null && empresa.isIntegracaoSpiviHabilitada());
            empresaDTO.setIntegracaoSpiviSourceName(empresa.getIntegracaoSpiviSourceName());
            empresaDTO.setIntegracaoSpiviSiteID(empresa.getIntegracaoSpiviSiteID());
            empresaDTO.setIntegracaoSpiviPassword(empresa.getIntegracaoSpiviPassword());
            empresaDTO.setUsarGestaoCreditosPersonal(empresa.getUsarGestaoCreditosPersonal());
            empresaDTO.setCarenciaRenovacao(empresa.getCarenciaRenovacao());
            empresaDTO.setTipoGestaoNfse(empresa.getTipoGestaoNfse());
            empresaDTO.setUtilizaGestaoClientesComRestricoes(empresa.getUtilizaGestaoClientesComRestricoes());
            empresaDTO.setHabilitarCadastroEmpresaSesi(empresa.isHabilitarCadastroEmpresaSesi());
            empresaDTO.setIntegracaoNuvemshopEmail(empresa.getIntegracaoNuvemshopEmail());
            empresaDTO.setIntegracaoNuvemshopNomeApp(empresa.getIntegracaoNuvemshopNomeApp());
            empresaDTO.setIntegracaoNuvemshopTokenAcesso(empresa.getIntegracaoNuvemshopTokenAcesso());
            empresaDTO.setIntegracaoNuvemshopHabilitada(empresa.isIntegracaoNuvemshopHabilitada());
            empresaDTO.setIntegracaoNuvemshopStoreId(empresa.getIntegracaoNuvemshopStoreId());
            empresaDTO.setPermiteContratosConcomintante(empresa.isPermiteContratosConcomintante());
            empresaDTO.setPontuarApenasCampanhasAtivas(empresa.getPontuarApenasCampanhasAtivas());
            empresaDTO.setUtilizarPactoPrint(empresa.getUtilizarPactoPrint());
            empresaDTO.setBvObrigatorio(empresa.getBvObrigatorio());
            empresaDTO.setUsarSescDf(empresa.getUsarSescDf());
            empresaDTO.setTokenSescDf(empresa.getTokenSescDf());
            empresaDTO.setUtilizaConfigCancelamentoSesc(empresa.getUtilizaConfigCancelamentoSesc());
            empresaDTO.setHorariocapacidadeporcategoria(empresa.getHorariocapacidadeporcategoria());
            return empresaDTO;
        }
        return null;
    }

    @Override
    public Empresa toEntity(EmpresaDTO empresaDTO) {
        if (empresaDTO != null) {
            Empresa empresa = new Empresa();
            empresa.setCodigo(empresaDTO.getCodigo());
            empresa.setNome(empresaDTO.getNome());
            empresa.setUsarNfce(empresaDTO.getUsarNfce());
            empresa.setUsarNfse(empresaDTO.getUsarNfse());
            empresa.setTrabalharComPontuacao(empresaDTO.getTrabalharComPontuacao());
            empresa.setAtiva(empresaDTO.getAtiva());
            empresa.setIntegracaoMyWellneHabilitada(empresaDTO.isIntegracaoMyWellneHabilitada());
            empresa.setIntegracaoMyWellnessEnviarVinculos(empresaDTO.isIntegracaoMyWellnessEnviarVinculos());
            empresa.setIntegracaoMyWellnessEnviarGrupos(empresaDTO.isIntegracaoMyWellnessEnviarGrupos());
            empresa.setIntegracaoMyWellnessFacilityUrl(empresaDTO.getIntegracaoMyWellnessFacilityUrl());
            empresa.setIntegracaMyWellneApiKey(empresaDTO.getIntegracaMyWellneApiKey());
            empresa.setIntegracaoMyWellnessUser(empresaDTO.getIntegracaoMyWellnessUser());
            empresa.setIntegracaoMyWellnessPassword(empresaDTO.getIntegracaoMyWellnessPassword());
            empresa.setNrDiasVigenciaMyWellnessGymPass(empresaDTO.getNrDiasVigenciaMyWellnessGymPass());
            empresa.setTipoVigenciaMyWellnessGympass(empresaDTO.getTipoVigenciaMyWellnessGympass());
            empresa.setIntegracaoMentorWebHabilitada(empresaDTO.isIntegracaoMentorWebHabilitada());
            empresa.setIntegracaoMentorWebUrl(empresaDTO.getIntegracaoMentorWebUrl());
            empresa.setIntegracaoMentorWebServico(empresaDTO.getIntegracaoMentorWebServico());
            empresa.setIntegracaoMentorWebUser(empresaDTO.getIntegracaoMentorWebUser());
            empresa.setIntegracaoMentorWebPassword(empresaDTO.getIntegracaoMentorWebPassword());
            empresa.setUtilizaSistemaEstacionamento(empresaDTO.isUtilizaSistemaEstacionamento());
            empresa.setArredondamento(empresaDTO.getArredondamento());
            if (empresaDTO.getEmpresaConfigEstacionamento() != null) {
                empresa.setEmpresaConfigEstacionamento(empresaConfigEstacionamentoAdapter.toEntity(empresaDTO.getEmpresaConfigEstacionamento()));
                empresa.getEmpresaConfigEstacionamento().setEmpresa(empresa);
            }
            empresa.setUsarParceiroFidelidade(empresaDTO.isUsarParceiroFidelidade());
            empresa.setNotificarWebhook(empresaDTO.isNotificarWebhook());
            empresa.setUrlWebhookNotificar(empresaDTO.getUrlWebhookNotificar());
            empresa.setIntegracaoAmigoFitHabilitada(empresaDTO.isIntegracaoAmigoFitHabilitada());
            empresa.setNomeUsuarioAmigoFit(empresaDTO.getNomeUsuarioAmigoFit());
            empresa.setSenhaUsuarioAmigoFit(empresaDTO.getSenhaUsuarioAmigoFit());
            empresa.setCpfCodigoInternoWeHelp(empresaDTO.isCpfCodigoInternoWeHelp());
            empresa.setTokenBuzzLead(empresaDTO.getTokenBuzzLead());
            empresa.setTokenSMS(empresaDTO.getTokenSMS());
            empresa.setTokenSMSShortCode(empresaDTO.getTokenSMSShortCode());
            empresa.setIntegracaoF360RelFatHabilitada(empresaDTO.isIntegracaoF360RelFatHabilitada());
            empresa.setIntegracaoF360FtpServer(empresaDTO.getIntegracaoF360FtpServer());
            empresa.setIntegracaoF360FtpPort(empresaDTO.getIntegracaoF360FtpPort());
            empresa.setIntegracaoF360User(empresaDTO.getIntegracaoF360User());
            empresa.setIntegracaoF360Password(empresaDTO.getIntegracaoF360Password());
            empresa.setIntegracaoF360Dir(empresaDTO.getIntegracaoF360Dir());
            empresa.setIntegracaoF360Quinzenal(empresaDTO.isIntegracaoF360Quinzenal());
            empresa.setUsarConciliadora(empresaDTO.isUsarConciliadora());
            empresa.setEmpresaConciliadora(empresaDTO.getEmpresaConciliadora());
            empresa.setSenhaConciliadora(empresaDTO.getSenhaConciliadora());
            empresa.setCodigoGymPass(empresaDTO.getCodigoGymPass());
            empresa.setTokenApiGymPass(empresaDTO.getTokenApiGymPass());
            empresa.setIntegracaoSpiviHabilitada(empresaDTO.isIntegracaoSpiviHabilitada());
            empresa.setIntegracaoSpiviSourceName(empresaDTO.getIntegracaoSpiviSourceName());
            empresa.setIntegracaoSpiviSiteID(empresaDTO.getIntegracaoSpiviSiteID());
            empresa.setIntegracaoSpiviPassword(empresaDTO.getIntegracaoSpiviPassword());
            empresa.setCarenciaRenovacao(empresaDTO.getCarenciaRenovacao());
            empresa.setTipoGestaoNfse(empresaDTO.getTipoGestaoNfse());
            empresa.setUtilizaGestaoClientesComRestricoes(empresaDTO.getUtilizaGestaoClientesComRestricoes());
            empresa.setIntegracaoNuvemshopNomeApp(empresaDTO.getIntegracaoNuvemshopNomeApp());
            empresa.setIntegracaoNuvemshopTokenAcesso(empresaDTO.getIntegracaoNuvemshopTokenAcesso());
            empresa.setIntegracaoNuvemshopEmail(empresaDTO.getIntegracaoNuvemshopEmail());
            empresa.setIntegracaoNuvemshopHabilitada(empresaDTO.isIntegracaoNuvemshopHabilitada());
            empresa.setIntegracaoNuvemshopStoreId(empresaDTO.getIntegracaoNuvemshopStoreId());
            empresa.setPontuarApenasCampanhasAtivas(empresaDTO.getPontuarApenasCampanhasAtivas());
            empresa.setUtilizarPactoPrint(empresaDTO.getUtilizarPactoPrint());
            empresa.setBvObrigatorio(empresaDTO.getBvObrigatorio());
            empresa.setUsarSescDf(empresaDTO.getUsarSescDf());
            empresa.setTokenSescDf(empresaDTO.getTokenSescDf());
            empresa.setUtilizaConfigCancelamentoSesc(empresa.getUtilizaConfigCancelamentoSesc());
            empresa.setHorariocapacidadeporcategoria(empresaDTO.isHorariocapacidadeporcategoria());

            return empresa;
        }
        return null;
    }
}
