package com.pacto.adm.core.adapters.objecao;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.objecao.ObjecaoDTO;
import com.pacto.adm.core.entities.objecao.Objecao;
import org.springframework.stereotype.Component;

@Component
public class ObjecaoAdapter implements AdapterInterface<Objecao, ObjecaoDTO> {
    @Override
    public ObjecaoDTO toDto(Objecao objecao) {
        ObjecaoDTO objecaoDTO = new ObjecaoDTO();

        objecaoDTO.setCodigo(objecao.getCodigo());
        objecaoDTO.setAtivo(objecao.getAtivo());
        objecaoDTO.setComentario(objecao.getComentario());
        objecaoDTO.setDescricao(objecao.getDescricao());
        objecaoDTO.setGrupo(objecao.getGrupo());
        objecaoDTO.setTipoGrupo(objecao.getTipoGrupo());

        return  objecaoDTO;
    }

    @Override
    public Objecao toEntity(ObjecaoDTO objecaoDTO) {
        Objecao objecao = new Objecao();

        objecao.setCodigo(objecaoDTO.getCodigo());
        objecao.setAtivo(objecaoDTO.getAtivo());
        objecao.setComentario(objecaoDTO.getComentario());
        objecao.setDescricao(objecaoDTO.getDescricao());
        objecao.setGrupo(objecaoDTO.getGrupo());
        objecao.setTipoGrupo(objecaoDTO.getTipoGrupo());

        return  objecao;
    }
}
