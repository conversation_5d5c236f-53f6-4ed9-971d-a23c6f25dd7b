package com.pacto.adm.core.adapters;

import com.pacto.adm.core.adapters.contrato.contratoduracao.ContratoDuracaoAdapter;
import com.pacto.adm.core.adapters.empresa.EmpresaAdapter;
import com.pacto.adm.core.dto.ContratoDTO;
import com.pacto.adm.core.entities.contrato.Contrato;
import com.pacto.adm.core.entities.contrato.SituacaoContratoEnum;
import org.springframework.stereotype.Component;

@Component
public class ContratoComAutorizacaoAdapter implements AdapterInterface<Contrato, ContratoDTO> {

    private final ContratoDuracaoAdapter contratoDuracaoAdapter;
    private final ClienteAdapter clienteAdapter;
    private final PessoaAdapter pessoaAdapter;
    private final PlanoAdapter planoAdapter;
    private final EmpresaAdapter empresaAdapter;

    public ContratoComAutorizacaoAdapter(ContratoDuracaoAdapter contratoDuracaoAdapter, ClienteAdapter clienteAdapter, PessoaAdapter pessoaAdapter, PlanoAdapter planoAdapter, EmpresaAdapter empresaAdapter) {
        this.contratoDuracaoAdapter = contratoDuracaoAdapter;
        this.clienteAdapter = clienteAdapter;
        this.pessoaAdapter = pessoaAdapter;
        this.planoAdapter = planoAdapter;
        this.empresaAdapter = empresaAdapter;
    }

    @Override
    public Contrato toEntity(ContratoDTO contratoDTO) {
        Contrato contrato = new Contrato();
        contrato = toEntity(contratoDTO, contrato);
        return contrato;
    }

    @Override
    public Contrato toEntity(ContratoDTO contratoDTO, Contrato contrato) {
        if (contratoDTO == null) {
            return null;
        }
        contrato.setCodigo(contratoDTO.getCodigo());
        contrato.setSituacaoContrato(SituacaoContratoEnum.valueOf(contratoDTO.getTipo()));
        return contrato;
    }

    @Override
    public ContratoDTO toDto(Contrato contrato) {
        ContratoDTO contratoDTO = new ContratoDTO();
        if (contrato != null) {
            contratoDTO.setCodigo(contrato.getCodigo());
            contratoDTO.setVigenciaDe(contrato.getVigenciaDe());
            contratoDTO.setVigenciaAteAjustada(contrato.getVigenciaAteAjustada());
            contratoDTO.setNomeModalidades(contrato.getNomeModalidades());
            contratoDTO.setEmailsCliente(contrato.getEmailsCliente());
            contratoDTO.setTelefonesCliente(contrato.getTelefonesCliente());
            contratoDTO.setSituacaoContratoSW(contrato.getSituacaoContratoSW());
            contratoDTO.setContratoRenovavel(contrato.getContratoRenovavel());
            contratoDTO.setTemAutorizacaoCobranca(contrato.getTemAutorizacaoCobranca());

            if (contrato.getContratoDuracao() != null) {
                contratoDTO.setContratoDuracao(contratoDuracaoAdapter.toDto(contrato.getContratoDuracao()));
            }

            if (contrato.getCliente() != null) {
                contratoDTO.setCliente(
                        clienteAdapter.toDto(contrato.getCliente())
                );
            }

            if (contrato.getPessoa() != null) {
                contratoDTO.setPessoaDTO(
                        pessoaAdapter.toDto(contrato.getPessoa())
                );
            }

            if (contrato.getPlano() != null) {
                contratoDTO.setPlano(
                        planoAdapter.toDto(contrato.getPlano())
                );
            }

            if (contrato.getEmpresa() != null) {
                contratoDTO.setEmpresa(
                        empresaAdapter.toDto(contrato.getEmpresa())
                );
            }
            return contratoDTO;
        }
        return null;
    }

}
