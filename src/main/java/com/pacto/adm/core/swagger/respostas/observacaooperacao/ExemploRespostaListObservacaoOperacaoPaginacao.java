package com.pacto.adm.core.swagger.respostas.observacaooperacao;

import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import com.pacto.adm.core.dto.observacaooperacao.ObservacaoOperacaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas das requisições envolvendo observações de operações com paginação")
public class ExemploRespostaListObservacaoOperacaoPaginacao extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<ObservacaoOperacaoDTO> content;

    public List<ObservacaoOperacaoDTO> getContent() {
        return content;
    }

    public void setContent(List<ObservacaoOperacaoDTO> content) {
        this.content = content;
    }

    public static final String resposta = "{"
            + "  \"content\": [{"
            + "    \"codigo\": 1001,"
            + "    \"justificativa\": \"Cancelamento solicitado pelo cliente devido a mudança de cidade\","
            + "    \"dataOperacao\": \"2024-01-15T10:30:00.000Z\","
            + "    \"tipoObservacao\": \"PC\","
            + "    \"movParcela\": {"
            + "      \"codigo\": 1,"
            + "      \"valor\": 199.99"
            + "    },"
            + "    \"valorOperacao\": 199.99,"
            + "    \"usuarioResponsavel\": \"João Silva\","
            + "    \"tipoCancelamento\": \"Manual\","
            + "    \"pessoa\": {"
            + "      \"codigo\": 12345,"
            + "      \"nome\": \"Maria Santos\""
            + "    },"
            + "    \"codigoCliente\": 12345,"
            + "    \"matriculaCliente\": \"MAT001234\","
            + "    \"codigoMatriculaCliente\": 5678"
            + "  }, {"
            + "    \"codigo\": 1002,"
            + "    \"justificativa\": \"Cancelamento por inadimplência\","
            + "    \"dataOperacao\": \"2024-01-16T14:20:00.000Z\","
            + "    \"tipoObservacao\": \"PC\","
            + "    \"movParcela\": {"
            + "      \"codigo\": 2,"
            + "      \"valor\": 149.99"
            + "    },"
            + "    \"valorOperacao\": 149.99,"
            + "    \"usuarioResponsavel\": \"Ana Costa\","
            + "    \"tipoCancelamento\": \"Manual\","
            + "    \"pessoa\": {"
            + "      \"codigo\": 67890,"
            + "      \"nome\": \"Carlos Oliveira\""
            + "    },"
            + "    \"codigoCliente\": 67890,"
            + "    \"matriculaCliente\": \"MAT005678\","
            + "    \"codigoMatriculaCliente\": 9012"
            + "  }],"
            + "  \"totalElements\": 2,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";
}
