package com.pacto.adm.core.entities;

import com.pacto.config.annotations.RelationalField;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;

@Entity
public class ProdutoParceiroFidelidade {
    @Id
    @GeneratedValue (strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String descricao;
    private Integer pontos;
    private Double valor;
    private String codigoExterno;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "parceiroFidelidade", foreignKey = @ForeignKey(name = "fk_produtoparceirofidelidade_parceirofidelidade"))
    @NotFound(action = NotFoundAction.IGNORE)
    private ParceiroFidelidade parceiroFidelidade;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getPontos() {
        return pontos;
    }

    public void setPontos(Integer pontos) {
        this.pontos = pontos;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getCodigoExterno() {
        return codigoExterno;
    }

    public void setCodigoExterno(String codigoExterno) {
        this.codigoExterno = codigoExterno;
    }

    public ParceiroFidelidade getParceiroFidelidade() {
        return parceiroFidelidade;
    }

    public void setParceiroFidelidade(ParceiroFidelidade parceiroFidelidade) {
        this.parceiroFidelidade = parceiroFidelidade;
    }
}
