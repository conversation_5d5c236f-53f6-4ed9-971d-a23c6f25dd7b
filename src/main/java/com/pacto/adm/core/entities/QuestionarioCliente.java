package com.pacto.adm.core.entities;

import com.pacto.config.annotations.NotLogged;
import com.pacto.config.annotations.RelationalField;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import java.util.Date;

@Entity
public class QuestionarioCliente {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer cliente;
    private String observacao;
    private Integer tipoBV;
    private Date data;
    private Date ultimaAtualizacao;
    @RelationalField
    @ManyToOne
    @JoinColumn(name = "evento", foreignKey = @ForeignKey(name = "fk_questionariocliente_evento"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Evento evento;

    @RelationalField
    @NotLogged
    @ManyToOne
    @JoinColumn(name = "questionario", foreignKey = @ForeignKey(name = "fk_questionariocliente_questionario"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Questionario questionario;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "consultor", foreignKey = @ForeignKey(name = "fk_questionariocliente_consultor"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Colaborador consultor;

    private Integer origemSistema;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Integer getTipoBV() {
        return tipoBV;
    }

    public void setTipoBV(Integer tipoBV) {
        this.tipoBV = tipoBV;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public Date getUltimaAtualizacao() {
        return ultimaAtualizacao;
    }

    public void setUltimaAtualizacao(Date ultimaAtualizacao) {
        this.ultimaAtualizacao = ultimaAtualizacao;
    }

    public Questionario getQuestionario() {
        return questionario;
    }

    public void setQuestionario(Questionario questionario) {
        this.questionario = questionario;
    }

    public Colaborador getConsultor() {
        return consultor;
    }

    public void setConsultor(Colaborador consultor) {
        this.consultor = consultor;
    }

    public Evento getEvento() {
        return evento;
    }

    public void setEvento(Evento evento) {
        this.evento = evento;
    }

    public Integer getOrigemSistema() {
        return origemSistema;
    }

    public void setOrigemSistema(Integer origemSistema) {
        this.origemSistema = origemSistema;
    }
}
