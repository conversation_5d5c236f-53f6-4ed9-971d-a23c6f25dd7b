package com.pacto.adm.core.entities.contrato;

import org.springframework.beans.BeanUtils;

import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;


@Entity
public class PlanoRecorrenciaParcela {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    @JoinColumn(name = "planoRecorrencia", foreignKey = @ForeignKey(name = "planorecorrenciaparcela_planorecorrencia_codigo_fk"))
    private PlanoRecorrencia planoRecorrencia;
    private int numero;
    private Double valor;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public PlanoRecorrencia getPlanoRecorrencia() {
        return planoRecorrencia;
    }

    public void setPlanoRecorrencia(PlanoRecorrencia planoRecorrencia) {
        this.planoRecorrencia = planoRecorrencia;
    }

    public int getNumero() {
        return numero;
    }

    public void setNumero(int numero) {
        this.numero = numero;
    }

    public Double getValor() {
        if (valor == null) {
            return 0D;
        }
        return new BigDecimal(valor).setScale(2, RoundingMode.HALF_UP).doubleValue();
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

}
