package com.pacto.adm.core.entities.contrato;

import com.pacto.adm.core.entities.horario.Horario;
import com.pacto.config.annotations.RelationalField;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "contratohorario")
public class ContratoHorario implements Cloneable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "horario", foreignKey = @ForeignKey(name = "fk_contratohorario_horario"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Horario horario;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "contrato", foreignKey = @ForeignKey(name = "fk_contratohorario_contrato"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Contrato contrato;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Horario getHorario() {
        return horario;
    }

    public void setHorario(Horario horario) {
        this.horario = horario;
    }

    public Contrato getContrato() {
        return contrato;
    }

    public void setContrato(Contrato contrato) {
        this.contrato = contrato;
    }
}
