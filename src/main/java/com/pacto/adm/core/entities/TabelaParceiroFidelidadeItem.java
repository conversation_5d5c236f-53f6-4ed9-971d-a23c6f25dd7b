package com.pacto.adm.core.entities;

import com.pacto.config.annotations.RelationalField;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;

@Entity
public class TabelaParceiroFidelidadeItem {
    @Id
    @GeneratedValue (strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Double valorInicio;
    private Double valorFim;
    private Double multiplicador;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "tabelaParceiroFidelidade", foreignKey = @ForeignKey(name = "fk_tabelaparceirofidelidadeitem_tabelaparceirofidelidade"))
    @NotFound(action = NotFoundAction.IGNORE)
    private TabelaParceiroFidelidade tabelaParceiroFidelidade;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Double getValorInicio() {
        return valorInicio;
    }

    public void setValorInicio(Double valorInicio) {
        this.valorInicio = valorInicio;
    }

    public Double getValorFim() {
        return valorFim;
    }

    public void setValorFim(Double valorFim) {
        this.valorFim = valorFim;
    }

    public Double getMultiplicador() {
        return multiplicador;
    }

    public void setMultiplicador(Double multiplicador) {
        this.multiplicador = multiplicador;
    }

    public TabelaParceiroFidelidade getTabelaParceiroFidelidade() {
        return tabelaParceiroFidelidade;
    }

    public void setTabelaParceiroFidelidade(TabelaParceiroFidelidade tabelaParceiroFidelidade) {
        this.tabelaParceiroFidelidade = tabelaParceiroFidelidade;
    }
}
