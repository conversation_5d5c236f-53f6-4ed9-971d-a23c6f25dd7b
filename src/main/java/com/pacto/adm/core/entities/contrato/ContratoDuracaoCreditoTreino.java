package com.pacto.adm.core.entities.contrato;

import com.pacto.config.annotations.NotLogged;
import com.pacto.adm.core.enumerador.TipoHorarioCreditoTreinoEnum;

import javax.persistence.*;
import java.util.Date;

@Entity
public class ContratoDuracaoCreditoTreino {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Enumerated(EnumType.ORDINAL)
    private TipoHorarioCreditoTreinoEnum tipoHorario;
    private Integer numeroVezesSemana;
    private Integer quantidadeCreditoCompra;
    private Integer quantidadeCreditoDisponivel;
    private Double valorUnitario;
    private boolean creditoTreinoNaoCumulativo;
    private Integer quantidadeCreditoMensal;
    private Date dataUltimoCreditoMensal;

    @NotLogged
    @OneToOne
    @JoinColumn(name = "contratoDuracao", foreignKey = @ForeignKey(name = "fk_contratodurcredtr_contdur"))
    private ContratoDuracao contratoDuracao;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public TipoHorarioCreditoTreinoEnum getTipoHorario() {
        return tipoHorario;
    }

    public void setTipoHorario(TipoHorarioCreditoTreinoEnum tipoHorario) {
        this.tipoHorario = tipoHorario;
    }

    public ContratoDuracao getContratoDuracao() {
        return contratoDuracao;
    }

    public Integer getNumeroVezesSemana() {
        return numeroVezesSemana;
    }

    public void setNumeroVezesSemana(Integer numeroVezesSemana) {
        this.numeroVezesSemana = numeroVezesSemana;
    }

    public Integer getQuantidadeCreditoCompra() {
        return quantidadeCreditoCompra;
    }

    public void setQuantidadeCreditoCompra(Integer quantidadeCreditoCompra) {
        this.quantidadeCreditoCompra = quantidadeCreditoCompra;
    }

    public Integer getQuantidadeCreditoDisponivel() {
        return quantidadeCreditoDisponivel;
    }

    public void setQuantidadeCreditoDisponivel(Integer quantidadeCreditoDisponivel) {
        this.quantidadeCreditoDisponivel = quantidadeCreditoDisponivel;
    }

    public Double getValorUnitario() {
        return valorUnitario;
    }

    public void setValorUnitario(Double valorUnitario) {
        this.valorUnitario = valorUnitario;
    }

    public boolean isCreditoTreinoNaoCumulativo() {
        return creditoTreinoNaoCumulativo;
    }

    public void setCreditoTreinoNaoCumulativo(boolean creditoTreinoNaoCumulativo) {
        this.creditoTreinoNaoCumulativo = creditoTreinoNaoCumulativo;
    }

    public Integer getQuantidadeCreditoMensal() {
        return quantidadeCreditoMensal;
    }

    public void setQuantidadeCreditoMensal(Integer quantidadeCreditoMensal) {
        this.quantidadeCreditoMensal = quantidadeCreditoMensal;
    }

    public Date getDataUltimoCreditoMensal() {
        return dataUltimoCreditoMensal;
    }

    public void setDataUltimoCreditoMensal(Date dataUltimoCreditoMensal) {
        this.dataUltimoCreditoMensal = dataUltimoCreditoMensal;
    }

    public void setContratoDuracao(ContratoDuracao contratoDuracao) {
        this.contratoDuracao = contratoDuracao;
    }
}
