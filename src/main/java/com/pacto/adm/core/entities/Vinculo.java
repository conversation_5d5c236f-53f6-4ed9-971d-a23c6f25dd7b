package com.pacto.adm.core.entities;

import com.pacto.config.annotations.RelationalField;

import javax.persistence.*;

@Entity
public class Vinculo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "colaborador", foreignKey = @ForeignKey(name = "fk_vinculo_colaborador"))
    private Colaborador colaborador;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "cliente", foreignKey = @ForeignKey(name = "fk_vinculo_cliente"))
    private Cliente cliente;

    public Vinculo() {
    }

    private String tipoVinculo;

    public Vinculo(Colaborador colaborador, Cliente cliente, String tipoVinculo) {
        this.colaborador = colaborador;
        this.cliente = cliente;
        this.tipoVinculo = tipoVinculo;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Colaborador getColaborador() {
        return colaborador;
    }

    public void setColaborador(Colaborador colaborador) {
        this.colaborador = colaborador;
    }

    public Cliente getCliente() {
        return cliente;
    }

    public void setCliente(Cliente cliente) {
        this.cliente = cliente;
    }

    public String getTipoVinculo() {
        return tipoVinculo;
    }

    public void setTipoVinculo(String tipoVinculo) {
        this.tipoVinculo = tipoVinculo;
    }
}
