package com.pacto.adm.core.entities.conveniodesconto;

import com.pacto.adm.core.entities.Usuario;
import com.pacto.adm.core.entities.empresa.Empresa;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "conveniodesconto")
public class ConvenioDesconto {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @Column(name = "dataassinatura", nullable = false)
    private Date dataAssinatura;

    @Column(name = "dataautorizacao", nullable = false)
    private Date dataAutorizacao;

    @Column(name = "datafinalvigencia", nullable = false)
    private Date dataFinalVigencia;

    @Column(name = "datainiciovigencia", nullable = false)
    private Date dataInicioVigencia;

    @Column(name = "descontoparcela")
    private Double descontoParcela;

    @Column(name = "descricao", nullable = false, length = 50)
    private String descricao;

    @ManyToOne
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(name = "fk_conveniodesconto_empresa"))
    private Empresa empresa;

    @Column(name = "isentarmatricula", columnDefinition = "DEFAULT FALSE")
    private Boolean isentarmatricula;

    @Column(name = "isentarrematricula", columnDefinition = "DEFAULT FALSE")
    private Boolean isentarRematricula;

    @ManyToOne
    @JoinColumn(name = "responsavelautorizacao", foreignKey = @ForeignKey(name = "fk_conveniodesconto_responsavelautorizacao"))
    private Usuario responsavelAutorizacao;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataAssinatura() {
        return dataAssinatura;
    }

    public void setDataAssinatura(Date dataAssinatura) {
        this.dataAssinatura = dataAssinatura;
    }

    public Date getDataAutorizacao() {
        return dataAutorizacao;
    }

    public void setDataAutorizacao(Date dataAutorizacao) {
        this.dataAutorizacao = dataAutorizacao;
    }

    public Date getDataFinalVigencia() {
        return dataFinalVigencia;
    }

    public void setDataFinalVigencia(Date dataFinalVigencia) {
        this.dataFinalVigencia = dataFinalVigencia;
    }

    public Date getDataInicioVigencia() {
        return dataInicioVigencia;
    }

    public void setDataInicioVigencia(Date dataInicioVigencia) {
        this.dataInicioVigencia = dataInicioVigencia;
    }

    public Double getDescontoParcela() {
        return descontoParcela;
    }

    public void setDescontoParcela(Double descontoParcela) {
        this.descontoParcela = descontoParcela;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Boolean getIsentarmatricula() {
        return isentarmatricula;
    }

    public void setIsentarmatricula(Boolean isentarmatricula) {
        this.isentarmatricula = isentarmatricula;
    }

    public Boolean getIsentarRematricula() {
        return isentarRematricula;
    }

    public void setIsentarRematricula(Boolean isentarRematricula) {
        this.isentarRematricula = isentarRematricula;
    }

    public Usuario getResponsavelAutorizacao() {
        return responsavelAutorizacao;
    }

    public void setResponsavelAutorizacao(Usuario responsavelAutorizacao) {
        this.responsavelAutorizacao = responsavelAutorizacao;
    }
}
