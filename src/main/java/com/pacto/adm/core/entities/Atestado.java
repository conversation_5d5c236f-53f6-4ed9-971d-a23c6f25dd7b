package com.pacto.adm.core.entities;

import javax.persistence.*;
import java.util.Date;

@Entity
public class Atestado {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Boolean parqPositivo;
    private String observacao;
    private Integer avaliacaoFisicaTW;
    private Date dataRegistro;

    @OneToOne
    @JoinColumn(name = "arquivo", foreignKey = @ForeignKey(name = "atestado_arquivo_fkey"))
    private Arquivo arquivo;

    @ManyToOne
    @JoinColumn(name = "movproduto", foreignKey = @ForeignKey(name = "atestado_movproduto_fkey"))
    private MovProduto movProduto;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getParqPositivo() {
        return parqPositivo;
    }

    public void setParqPositivo(Boolean parqPositivo) {
        this.parqPositivo = parqPositivo;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Integer getAvaliacaoFisicaTW() {
        return avaliacaoFisicaTW;
    }

    public void setAvaliacaoFisicaTW(Integer avaliacaoFisicaTW) {
        this.avaliacaoFisicaTW = avaliacaoFisicaTW;
    }

    public Arquivo getArquivo() {
        return arquivo;
    }

    public void setArquivo(Arquivo arquivo) {
        this.arquivo = arquivo;
    }

    public MovProduto getMovProduto() {
        return movProduto;
    }

    public void setMovProduto(MovProduto movProduto) {
        this.movProduto = movProduto;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }
}
