package com.pacto.adm.core.entities.contrato;

import com.pacto.adm.core.entities.Arquivo;
import com.pacto.adm.core.entities.ProdutoSugerido;
import com.pacto.config.annotations.NomeEntidadeLog;
import com.pacto.config.annotations.NotLogged;
import org.springframework.beans.BeanUtils;

import javax.persistence.CascadeType;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


@Entity
@NomeEntidadeLog("Modalidade")
public class Modalidade implements Cloneable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nome;
    private Integer nrVezes;
    private Boolean modalidadeDefault;
    private Boolean utilizarturma;
    private Boolean utilizarProduto;
    private Double valormensal;
    private Boolean ativo;
    private Boolean modalidadeCampeonato;
    private Boolean usaTreino;
    private Boolean crossfit;
    private String fotoKey;

    @OneToOne
    @JoinColumn(name = "tipo", referencedColumnName = "identificador", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private TipoModalidade tipo;

    private Boolean integracaoSpivi;
    private Boolean modalidadeTurmaParaSelecionar;

    @OneToOne(orphanRemoval = true)
    @JoinColumn(name = "arquivo", foreignKey = @ForeignKey(name="modalidade_arquivo_fkey"))
    private Arquivo arquivo;

    @NotLogged
    @OneToMany(mappedBy = "modalidade", cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REMOVE}, orphanRemoval = true)
    private List<ModalidadeEmpresa> modalidadeEmpresa;

    @NotLogged
    @OneToMany(mappedBy = "modalidade", cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REMOVE}, orphanRemoval = true)
    private List<ProdutoSugerido> produtosSugeridos;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getNrVezes() {
        return nrVezes;
    }

    public void setNrVezes(Integer nrVezes) {
        this.nrVezes = nrVezes;
    }

    public Boolean getModalidadeDefault() {
        return modalidadeDefault;
    }

    public void setModalidadeDefault(Boolean modalidadeDefault) {
        this.modalidadeDefault = modalidadeDefault;
    }

    public Boolean getUtilizarturma() {
        return utilizarturma;
    }

    public void setUtilizarturma(Boolean utilizarturma) {
        this.utilizarturma = utilizarturma;
    }

    public Boolean getUtilizarProduto() {
        return utilizarProduto;
    }

    public void setUtilizarProduto(Boolean utilizarProduto) {
        this.utilizarProduto = utilizarProduto;
    }

    public Double getValormensal() {
        return valormensal;
    }

    public void setValormensal(Double valormensal) {
        this.valormensal = valormensal;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public Boolean getModalidadeCampeonato() {
        return modalidadeCampeonato;
    }

    public void setModalidadeCampeonato(Boolean modalidadeCampeonato) {
        this.modalidadeCampeonato = modalidadeCampeonato;
    }

    public Boolean getUsaTreino() {
        return usaTreino;
    }

    public void setUsaTreino(Boolean usaTreino) {
        this.usaTreino = usaTreino;
    }

    public Boolean getCrossfit() {
        if (crossfit == null) {
            crossfit = false;
        }
        return crossfit;
    }

    public void setCrossfit(Boolean crossfit) {
        this.crossfit = crossfit;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public TipoModalidade getTipo() {
        return tipo;
    }

    public void setTipo(TipoModalidade tipo) {
        this.tipo = tipo;
    }

    public Boolean getIntegracaoSpivi() {
        return integracaoSpivi;
    }

    public void setIntegracaoSpivi(Boolean integracaoSpivi) {
        this.integracaoSpivi = integracaoSpivi;
    }

    public Boolean getModalidadeTurmaParaSelecionar() {
        return modalidadeTurmaParaSelecionar;
    }

    public void setModalidadeTurmaParaSelecionar(Boolean modalidadeTurmaParaSelecionar) {
        this.modalidadeTurmaParaSelecionar = modalidadeTurmaParaSelecionar;
    }

    public Arquivo getArquivo() {
        return arquivo;
    }

    public void setArquivo(Arquivo arquivo) {
        this.arquivo = arquivo;
    }

    public List<ModalidadeEmpresa> getModalidadeEmpresa() {
        return modalidadeEmpresa;
    }

    public void setModalidadeEmpresa(List<ModalidadeEmpresa> modalidadeEmpresa) {
        this.modalidadeEmpresa = modalidadeEmpresa;
    }

    public List<ProdutoSugerido> getProdutosSugeridos() {
        return produtosSugeridos;
    }

    public void setProdutosSugeridos(List<ProdutoSugerido> produtosSugeridos) {
        this.produtosSugeridos = produtosSugeridos;
    }

    @Override
    public Modalidade clone() {
        Modalidade modalidade = new Modalidade();
        BeanUtils.copyProperties(this, modalidade, "modalidadeEmpresa", "produtosSugeridos");

        if (this.getModalidadeEmpresa() != null) {
            List<ModalidadeEmpresa> clonedModalidadeEmpresa = new ArrayList<>();
            for (ModalidadeEmpresa empresa : this.getModalidadeEmpresa()) {
                try {
                    clonedModalidadeEmpresa.add(empresa.clone());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            modalidade.setModalidadeEmpresa(clonedModalidadeEmpresa);
        }

        if (this.getProdutosSugeridos() != null) {
            List<ProdutoSugerido> clonedProdutosSugeridos = new ArrayList<>();
            for (ProdutoSugerido produto : this.getProdutosSugeridos()) {
                try {
                    clonedProdutosSugeridos.add(produto.clone());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            modalidade.setProdutosSugeridos(clonedProdutosSugeridos);
        }

        BeanUtils.copyProperties(this, modalidade);
        return modalidade;
    }
}
