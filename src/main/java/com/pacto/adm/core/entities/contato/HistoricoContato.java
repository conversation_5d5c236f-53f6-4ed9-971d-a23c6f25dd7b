package com.pacto.adm.core.entities.contato;



import com.pacto.adm.core.enumerador.contato.TipoGymBotEnum;

import javax.persistence.*;


@Entity
@Table(name = "historicocontato")
public class HistoricoContato {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private int fluxoGymBot;
    private TipoGymBotEnum tipoGymBotEnum;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public int getFluxoGymBot() {
        return fluxoGymBot;
    }

    public void setFluxoGymBot(int fluxoGymBot) {
        this.fluxoGymBot = fluxoGymBot;
    }

    public TipoGymBotEnum getTipoGymBotEnum() {
        return tipoGymBotEnum;
    }

    public void setTipoGymBotEnum(TipoGymBotEnum tipoGymBotEnum) {
        this.tipoGymBotEnum = tipoGymBotEnum;
    }
}
