package com.pacto.adm.core.entities;

import com.pacto.adm.core.enumerador.PerfilUsuarioEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Transient;
import java.util.List;

@Entity
public class PerfilAcesso {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "Código único identificador do perfil de acesso", example = "1")
    private Integer codigo;

    @Schema(description = "Nome do perfil de acesso", example = "PROFESSOR")
    private String nome;

    @Schema(
            description = "Tipo do perfil de acesso.<br/>" +
                    "Tipos disponíveis" +
                    "<ul>" +
                    "<li>Todos</li>" +
                    "<li>Administrador</li>" +
                    "<li>Consultor</li>" +
                    "<li><PERSON><PERSON><PERSON></li>" +
                    "<li>Professor</li>" +
                    "</ul>",
            example = "1",
            implementation = PerfilUsuarioEnum.class
    )
    private PerfilUsuarioEnum tipo;
    @Transient

    @Schema(description = "Permissões liberadas para o Perfil de Acesso")
    private List<Permissao> permissaoVOs;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public PerfilUsuarioEnum getTipo() {
        return tipo;
    }

    public void setTipo(PerfilUsuarioEnum tipo) {
        this.tipo = tipo;
    }

    public List<Permissao> getPermissaoVOs() {
        return permissaoVOs;
    }

    public void setPermissaoVOs(List<Permissao> permissaoVOs) {
        this.permissaoVOs = permissaoVOs;
    }
}
