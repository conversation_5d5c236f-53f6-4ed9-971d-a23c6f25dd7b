package com.pacto.adm.core.objects;

import com.pacto.adm.core.entities.MovPagamento;
import com.pacto.adm.core.entities.MovParcela;
import com.pacto.adm.core.entities.ReciboPagamento;
import com.pacto.adm.core.entities.Usuario;
import com.pacto.adm.core.entities.financeiro.RemessaItem;
import com.pacto.adm.core.entities.financeiro.Transacao;

import java.util.ArrayList;
import java.util.List;

public class EstornoRecibo {

    protected ReciboPagamento reciboPagamento;
    protected Usuario responsavelEstornoRecivo;
    protected List<MovParcela> listaMovParcela;
    protected List<MovPagamento> listaMovPagamento;
    private List<Transacao> listaTransacoes;
    private List<RemessaItem> listaItensRemessa;
    private boolean estornarOperadora = true;
    private boolean excluirNFSe = false; // atributo transient
    private boolean mostrarMsgExcluirNFse = false; // atributo transient
    private boolean validarNFSeProdutosPagos = true; // notas de referentes a compentencia podem ou não ser excluídas. Por padrão sim, pois já era um comportamento do sistema

    public ReciboPagamento getReciboPagamento() {
        return reciboPagamento;
    }

    public void setReciboPagamento(ReciboPagamento reciboPagamento) {
        this.reciboPagamento = reciboPagamento;
    }

    public Usuario getResponsavelEstornoRecivo() {
        return responsavelEstornoRecivo;
    }

    public void setResponsavelEstornoRecivo(Usuario responsavelEstornoRecivo) {
        this.responsavelEstornoRecivo = responsavelEstornoRecivo;
    }

    public List<MovParcela> getListaMovParcela() {
        return listaMovParcela;
    }

    public void setListaMovParcela(List<MovParcela> listaMovParcela) {
        this.listaMovParcela = listaMovParcela;
    }

    public List<MovPagamento> getListaMovPagamento() {
        return listaMovPagamento;
    }

    public void setListaMovPagamento(List<MovPagamento> listaMovPagamento) {
        this.listaMovPagamento = listaMovPagamento;
    }

    public List<Transacao> getListaTransacoes() {
        if (listaTransacoes == null) {
            listaTransacoes = new ArrayList<>();
        }
        return listaTransacoes;
    }

    public void setListaTransacoes(List<Transacao> listaTransacoes) {
        this.listaTransacoes = listaTransacoes;
    }

    public List<RemessaItem> getListaItensRemessa() {
        return listaItensRemessa;
    }

    public void setListaItensRemessa(List<RemessaItem> listaItensRemessa) {
        this.listaItensRemessa = listaItensRemessa;
    }

    public boolean isEstornarOperadora() {
        return estornarOperadora;
    }

    public void setEstornarOperadora(boolean estornarOperadora) {
        this.estornarOperadora = estornarOperadora;
    }

    public boolean isExcluirNFSe() {
        return excluirNFSe;
    }

    public void setExcluirNFSe(boolean excluirNFSe) {
        this.excluirNFSe = excluirNFSe;
    }

    public boolean isMostrarMsgExcluirNFse() {
        return mostrarMsgExcluirNFse;
    }

    public void setMostrarMsgExcluirNFse(boolean mostrarMsgExcluirNFse) {
        this.mostrarMsgExcluirNFse = mostrarMsgExcluirNFse;
    }

    public boolean isValidarNFSeProdutosPagos() {
        return validarNFSeProdutosPagos;
    }

    public void setValidarNFSeProdutosPagos(boolean validarNFSeProdutosPagos) {
        this.validarNFSeProdutosPagos = validarNFSeProdutosPagos;
    }
}
