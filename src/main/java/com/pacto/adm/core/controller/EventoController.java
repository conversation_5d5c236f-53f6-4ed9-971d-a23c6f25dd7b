package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.evento.EnvelopeRespostaListEvento;
import com.pacto.adm.core.services.interfaces.EventoService;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@RestController
@RequestMapping("/eventos")
@Tag(name = "Evento")
public class EventoController {

    @Autowired
    private EventoService eventoService;

    @Operation(
            summary = "Consultar todos os eventos",
            description = "Consulta todos os eventos da academia",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListEvento.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListEvento.resposta)
                            )
                    )}
    )
    @GetMapping("")
    public ResponseEntity<EnvelopeRespostaDTO> findAll() {
        try {
            return ResponseEntityFactory.ok(eventoService.findAll());
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Operation(
            summary = "Consultar todos os eventos que acontecem hoje",
            description = "Consulta todos os eventos da academia que acontecem na data de hoje.",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListEvento.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListEvento.resposta)
                            )
                    )}
    )
    @GetMapping("/today")
    public ResponseEntity<EnvelopeRespostaDTO> findAllToday() {
        try {
            return ResponseEntityFactory.ok(eventoService.findAllToday());
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @GetMapping("/by-date")
    public ResponseEntity<EnvelopeRespostaDTO> findAllByDate(@RequestParam String date, @RequestParam(required = false) String descricao) {
        try {
            return ResponseEntityFactory.ok(eventoService.findAllByDate(descricao, new Date(Long.parseLong(date))));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
