package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.VendaAvulsaDTO;
import com.pacto.adm.core.dto.enveloperesposta.desconto.EnvelopeRespostaListDescontoDTO;
import com.pacto.adm.core.dto.enveloperesposta.pacotepersonal.EnvelopeRespostaListPacotePersonalDTO;
import com.pacto.adm.core.dto.enveloperesposta.pessoa.EnvelopeRespostaListPessoaDTO;
import com.pacto.adm.core.dto.enveloperesposta.pessoa.EnvelopeRespostaPessoaDTO;
import com.pacto.adm.core.dto.enveloperesposta.vendaavulsa.EnvelopeRespostaResultadoVendaDTO;
import com.pacto.adm.core.dto.enveloperesposta.vendaavulsa.EnvelopeRespostaResultadoVendaV2DTO;
import com.pacto.adm.core.dto.enveloperesposta.vendaavulsa.EnvelopeRespostaVendaAvulsaDTO;
import com.pacto.adm.core.dto.negociacao.ResultadoVendaDTO;
import com.pacto.adm.core.dto.negociacao.ResultadoVendaV2DTO;
import com.pacto.adm.core.services.interfaces.ProdutoService;
import com.pacto.adm.core.services.interfaces.VendaAvulsaService;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import com.pacto.config.utils.UteisValidacao;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/venda-avulsa")
public class VendaAvulsaController {

    @Autowired
    private VendaAvulsaService service;
    @Autowired
    private ProdutoService serviceProduto;

    @Operation(
            summary = "Consultar compradores",
            description = "Consulta as informações dos clientes que já realizaram compras de produtos de forma avulsa no sistema.",
            tags = {"Venda Avulsa"},
            parameters = {
                    @Parameter(
                            name = "quickSearch",
                            description = "Filtra os resultados dos compradores pelo nome informado.",
                            example = "João"
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListPessoaDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaListPessoaDTO.resposta),
                                    }
                            )
                    )
            }
    )
    @GetMapping("/compradores")
    public ResponseEntity<EnvelopeRespostaDTO> compradores(@RequestParam(value = "quickSearch", required = false) String quickSearch) {
        try {
            if (UteisValidacao.emptyString(quickSearch) || quickSearch.equals("null")) {
                return ResponseEntityFactory.ok(service.consultarCompradores(null, null, true));
            }
            return ResponseEntityFactory.ok(service.consultarCompradores(quickSearch, null, false));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar comprador",
            description = "Consulta as informações de um comprador pelo código de matrícula dele.",
            tags = {"Venda Avulsa"},
            parameters = {
                    @Parameter(
                            name = "matricula",
                            description = "Filtra os resultados pela matricula do comprador.",
                            example = "1"
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListPessoaDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaListPessoaDTO.resposta),
                                    }
                            )
                    )
            }
    )
    @GetMapping("/cliente/{matricula}")
    public ResponseEntity<EnvelopeRespostaDTO> cliente(@PathVariable Integer matricula) {
        try {
            return ResponseEntityFactory.ok(service.consultarCompradores(null, matricula, false));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar pacotes",
            description = "Consulta as informações dos pacotes pelos produtos associados a eles.",
            tags = {"Venda Avulsa"},
            parameters = {
                    @Parameter(
                            name = "produto",
                            description = "Código do produto vinculado aos pacotes.",
                            example = "1",
                            required = true
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListPacotePersonalDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaListPacotePersonalDTO.resposta),
                                    }
                            )
                    )
            }
    )
    @GetMapping("/pacotes/{produto}")
    public ResponseEntity<EnvelopeRespostaDTO> pacotes(@PathVariable Integer produto) {
        try {
            return ResponseEntityFactory.ok(service.consultarPacotes(produto));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar descontos por tipo de produto",
            description = "Consulta os descontos por tipo de produto.",
            tags = {"Venda Avulsa"},
            parameters = {
                    @Parameter(
                            name = "empresaId",
                            description = "Código da empresa que será consultado os descontos.",
                            example = "1",
                            required = true
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListDescontoDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaListDescontoDTO.resposta),
                                    }
                            )
                    )
            }
    )
    @GetMapping("/descontos")
    public ResponseEntity<EnvelopeRespostaDTO> descontos(@RequestHeader(value = "empresaId") Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(serviceProduto.descontosTipoProdutoVenda("VG", empresaId));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar as informações de uma pessoa",
            description = "Consulta as informações de uma pessoa.",
            tags = {"Pessoa"},
            parameters = {
                    @Parameter(
                            name = "pessoa",
                            description = "Código da pessoa que será consultada.",
                            example = "1",
                            required = true
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaPessoaDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaPessoaDTO.resposta),
                                    }
                            )
                    )
            }
    )
    @GetMapping("/pessoa/{pessoa}")
    public ResponseEntity<EnvelopeRespostaDTO> pessoa(@PathVariable Integer pessoa) {
        try {
            return ResponseEntityFactory.ok(service.pessoaCodigo(pessoa));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Cadastrar uma venda avulsa (V1)",
            description = "Cadastra uma venda avulsa. <strong> Está é a primeira versão, é recomendável utilizar a V2.</strong>",
            tags = {"Venda Avulsa"},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para cadastrar uma nova venda avulsa",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = VendaAvulsaDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaVendaAvulsaDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaResultadoVendaDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaResultadoVendaDTO.resposta),
                                    }
                            )
                    )
            }
    )
    @PostMapping()
    public ResponseEntity<EnvelopeRespostaDTO> vendaAvulsa(@RequestBody VendaAvulsaDTO vendaAvulsaDTO) {
        HttpHeaders headers = new HttpHeaders();
        try {
            headers.setContentType(MediaType.APPLICATION_JSON);
            ResultadoVendaDTO vendaAvulsa = service.incluirVenda(vendaAvulsaDTO);
            return ResponseEntityFactory.ok(vendaAvulsa);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Cadastrar uma venda avulsa (V2)",
            description = "Cadastra uma venda avulsa trazendo informações da venda cadastrada e as parcelas vinculadas à venda.</strong>",
            tags = {"Venda Avulsa"},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para cadastrar uma nova venda avulsa",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = VendaAvulsaDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaVendaAvulsaDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaResultadoVendaV2DTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaResultadoVendaV2DTO.resposta),
                                    }
                            )
                    )
            }
    )
    @PostMapping("/v2")
    public ResponseEntity<EnvelopeRespostaDTO> vendaAvulsav2(@RequestBody VendaAvulsaDTO vendaAvulsaDTO) {
        HttpHeaders headers = new HttpHeaders();
        try {
            headers.setContentType(MediaType.APPLICATION_JSON);
            ResultadoVendaV2DTO vendaAvulsa = service.incluirVendav2(vendaAvulsaDTO);
            return ResponseEntityFactory.ok(vendaAvulsa);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
