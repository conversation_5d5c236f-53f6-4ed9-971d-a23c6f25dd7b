package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.FreepassDTO;
import com.pacto.adm.core.dto.enveloperesposta.acesso.freepass.EnvelopeRespostaFreePassDTO;
import com.pacto.adm.core.dto.enveloperesposta.acesso.periodo.EnvelopeRespostaPeriodoAcessoClienteDTO;
import com.pacto.adm.core.dto.enveloperesposta.log.EnvelopeRespostaListLogDTO;
import com.pacto.adm.core.dto.enveloperesposta.produto.EnvelopeRespostaListProdutoDTO;
import com.pacto.adm.core.dto.enveloperesposta.tiposvariaveis.EnvelopeRespostaBoolan;
import com.pacto.adm.core.dto.filtros.FiltroLogClienteJSON;
import com.pacto.adm.core.services.interfaces.FreepassService;
import com.pacto.adm.core.services.interfaces.ProdutoService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/freepass")
public class FreepassController {

    @Autowired
    private ProdutoService produtoService;
    @Autowired
    private FreepassService freepassService;
    @Autowired
    private RequestService requestService;


    @Operation(
            summary = "Consultar todos os produtos que são FreePass",
            description = "Consulta todos os produtos que são FreePass.",
            tags = {"Produto"},
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListProdutoDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListProdutoDTO.resposta)
                            )
                    )}
    )
    @GetMapping("/produtos")
    public ResponseEntity<EnvelopeRespostaDTO> findProdutosFreepass() {
        try {
            return ResponseEntityFactory.ok(produtoService.findAllByTipoProduto("FR"));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar todos os produtos que são FreePass e que estão ativos",
            description = "Consulta todos os produtos que são FreePass e que estão ativos.",
            tags = {"Produto"},
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListProdutoDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListProdutoDTO.resposta)
                            )
                    )}
    )
    @GetMapping("/produtos/ativos")
    public ResponseEntity<EnvelopeRespostaDTO> findProdutosFreepassAtivos() {
        try {
            return ResponseEntityFactory.ok(produtoService.findAllByTipoProdutoAtivo("FR", true));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar período de acesso de um cliente efetuado com Free Pass",
            description = "Consulta o período de acesso de um cliente efeutado com Free Pass.",
            tags = {"Lista de Acessos"},
            parameters = {
                    @Parameter(name = "cliente", description = "Código do cliente que se deseja consultar", example = "234")
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaPeriodoAcessoClienteDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaPeriodoAcessoClienteDTO.resposta)
                            )
                    )}
    )
    @GetMapping("/{cliente}")
    public ResponseEntity<EnvelopeRespostaDTO> findFreepassByCliente(@PathVariable Integer cliente) {
        try {
            return ResponseEntityFactory.ok(freepassService.findByCliente(cliente));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Cadastrar acesso de um cliente com Free Pass",
            description = "Cadastrar o acesso de um cliente realizado com Free Pass.",
            tags = {"Freepass"},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para criar ou atualizar.<br/>" +
                            "Se for criar um novo registro, envie a requisição sem o atributo código.<br/>" +
                            "Se for atualizar um registro, envie a requisição com o atribbuto código contendo o valor do registro que será atualizado.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = FreepassDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaFreePassDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaPeriodoAcessoClienteDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaPeriodoAcessoClienteDTO.resposta)
                            )
                    )}
    )
    @PostMapping("")
    public ResponseEntity<EnvelopeRespostaDTO> gravarFreepass(@RequestBody FreepassDTO freepass) {
        try {
            if (freepass != null && freepass.getResponsavel() == null) {
                freepass.setResponsavel(requestService.getUsuarioAtual().getCodZw());
            }
            return ResponseEntityFactory.ok(freepassService.gravarFreepass(freepass));
        } catch (ServiceException e) {
            e.printStackTrace();
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Operation(
            summary = "Excluir acesso de um cliente realizado com Free Pass",
            description = "Excluir o acesso de um cliente realizado com Free Pass.",
            tags = {"Freepass"},
            parameters = {
                    @Parameter(name = "codCliente", description = "Código do cliente que será removido o acesso com Free Pass")
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaBoolan.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaBoolan.respostaTrue)
                            )
                    )}
    )
    @DeleteMapping("/{codCliente}")
    public ResponseEntity<EnvelopeRespostaDTO> removeFreepass(@PathVariable Integer codCliente) {
        try {
            return ResponseEntityFactory.ok(freepassService.removerFreepass(codCliente));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Operation(
            summary = "Consultar Logs de acesso realizados com Free Pass",
            description = "Consulta os logs de acesso realizados com Free Pass.",
            tags = {"Logs"},
            parameters = {
                    @Parameter(
                            name = "filters",
                            description = "Filtro de busca avançada para logs ou registros.<br/>" +
                                    "<br/><strong>Filtros disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>quicksearchValue:</strong> Texto utilizado para busca parcial nos campos:<br/>" +
                                    "- <em>operacao</em> (campo 'obj.operacao')<br/>" +
                                    "- <em>nomeEntidadeDescricao</em> (campo 'obj.nomeEntidadeDescricao').</li>" +
                                    "<li><strong>dataInicio:</strong> Data inicial para o intervalo de busca em <em>dataAlteracao</em> (formato yyyy-MM-dd).</li>" +
                                    "<li><strong>dataFim:</strong> Data final para o intervalo de busca em <em>dataAlteracao</em> (formato yyyy-MM-dd).</li>" +
                                    "<li><strong>tipo:</strong> Lista de tipos (strings) para filtrar registros cuja operação contenha os valores informados.</li>" +
                                    "</ul>",
                            example = "{\"quicksearchValue\":\"financeiro\",\"dataInicio\":\"2024-01-01\",\"dataFim\":\"2024-12-31\",\"tipo\":[\"ALTERACAO\",\"EXCLUSAO\"]}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas pelos atributos disponíveis.<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>operacao:</strong> Ordena pela operação</li>" +
                                    "<li><strong>usuario:</strong> Ordena pelo usuário</li>" +
                                    "<li><strong>dataAlteracao:</strong> Ordena pela data da alteração</li>" +
                                    "<li><strong>codigo:</strong> Ordena pelo código</li>" +
                                    "<li><strong>alteracoes:</strong> Ordena pelas alterações</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para fazer a ordenação, utilize o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas por <strong>dataAlteracao</strong> (descendente) e depois por <strong>codigo</strong> (descendente).<br/><br/>",
                            example = "dataAlteracao,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListLogDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListLogDTO.resposta)
                            )
                    )}
    )
    @GetMapping("/logs")
    public ResponseEntity<EnvelopeRespostaDTO> buscarLogsFreepass(@RequestParam(value = "filters", required = false) JSONObject filters, @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroLogClienteJSON filtros = new FiltroLogClienteJSON(filters);
            return ResponseEntityFactory.ok(freepassService.buscarLogs(filtros, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

}
