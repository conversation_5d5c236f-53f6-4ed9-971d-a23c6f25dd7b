package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.entities.InfoMigracao;
import com.pacto.adm.core.enumerador.OrigemSistemaEnum;
import com.pacto.adm.core.enumerador.TipoInfoMigracaoEnum;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Informações de Migração", description = "Informações de migração no sistema")
public class InfoMigracaoDTO {


    @Schema(description = "Código único identificador da migração", example = "1")
    private Integer codigo;

    @Schema(description = "Tipo da informação de migração.<br/> " +
            "<strong>Valores disponíveis</strong>" +
            "<ul>" +
            "<li>1 - NEGOCIACAO</li>" +
            "<li>2 - CONTRATO_LANCADO</li>" +
            "<li>3 - LISTA_PESSOAS</li>" +
            "<li>4 - TELA_ALUNO</li>" +
            "<li>5 - VENDA_AVULSA</li>" +
            "<li>6 - CONFIGURACOES</li>" +
            "<li>7 - CAIXA_ABERTO</li>" +
            "<li>8 - INCLUIR_CLIENTE</li>" +
            "</ul>",
            example = "1", implementation = TipoInfoMigracaoEnum.class)
    private Integer tipoInfo;

    @Schema(description = "Código único identificador do usuário responsável pela informação de migração", example = "2")
    private Integer usuario;

    @Schema(description = "Indica se o recurso está ativo no sistema ou não", example = "true")
    private String info;

    @Schema(description = "Origem do sistema" +
            "<strong>Valores disponíveis</strong>" +
            "<ul>" +
            "<li>1 - ZW (ZillyonWeb)</li>" +
            "<li>2 - AULA_CHEIA (Agenda Web)</li>" +
            "<li>3 - TREINO (Pacto Treino)</li>" +
            "<li>4 - APP_TREINO (App Treino)</li>" +
            "<li>5 - APP_PROFESSOR (App Professor)</li>" +
            "<li>6 - AUTO_ATENDIMENTO (Autoatendimento)</li>" +
            "<li>7 - SITE (Site Vendas)</li>" +
            "<li>8 - BUZZLEAD (Buzz Lead)</li>" +
            "<li>9 - VENDAS_ONLINE_2 (Vendas 2.0)</li>" +
            "<li>10 - APP_CONSULTOR (App do consultor)</li>" +
            "<li>11 - BOOKING_GYMPASS (Booking Gympass)</li>" +
            "<li>12 - FILA_ESPERA (Fila de espera)</li>" +
            "<li>13 - IMPORTACAO_API (Importação API)</li>" +
            "<li>14 - HUBSPOT (Hubspot Lead)</li>" +
            "<li>15 - CRM_META_DIARIA (CRM Meta Diaria)</li>" +
            "<li>16 - APP_FLOW (Pacto Flow)</li>" +
            "<li>17 - NOVA_TELA_NEGOCIACAO (Nova Tela de Negociação)</li>" +
            "</ul>"
            , example = "1", implementation = OrigemSistemaEnum.class)
    private String origem;

    public InfoMigracaoDTO() {

    }

    public InfoMigracaoDTO(InfoMigracao obj) {
        this.codigo = obj.getCodigo();
        this.tipoInfo = obj.getTipoInfo();
        this.usuario = obj.getUsuario() != null ? obj.getUsuario().getCodigo() : null;
        this.info = obj.getInfo();
        this.origem = obj.getOrigem();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getTipoInfo() {
        return tipoInfo;
    }

    public void setTipoInfo(Integer tipoInfo) {
        this.tipoInfo = tipoInfo;
    }

    public Integer getUsuario() {
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }
}
