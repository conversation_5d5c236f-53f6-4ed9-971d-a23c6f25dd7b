package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pacto.adm.core.enumerador.clienterestricao.TipoClienteRestricaoEnum;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonIgnoreProperties(ignoreUnknown = true)
@Schema(name = "Restrição do Cliente", description = "Informações de restrição do cliente")
public class ClienteRestricaoDTO {

    @Schema(description = "Código da restrição", example = "394")
    private Integer codigo;

    @Schema(description = "Nome do cliente", example = "Augusto Sanches")
    private String nome;

    @Schema(description = "Código da matrícula do cliente", example = "543209")
    private Integer codigoMatricula;

    @Schema(description = "CPF do cliente", example = "455.123.234-10")
    private String cpf;

    @Schema(description = "Observação do cliente", example = "Inadimplência, cliente não está mais perdido acessar a academia até pagar todos os débitos")
    private String observacao;

    @Schema(description = "Código da empresa que o cliente está com restrição", example = "ACADEMIA PACTO")
    private Integer codigoEmpresa;

    @Schema(description = "Nome da empresa que o cliente está com restrição", example = "1")
    private String nomeEmpresa;

    @Schema(description = "Chave da empresa que o cliente está com restrição", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9")
    private String chaveEmpresa;

    @Schema(description = "Tipo da restrição do cliente", example = "IN", implementation = TipoClienteRestricaoEnum.class)
    private String tipo;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCodigoMatricula() {
        return codigoMatricula;
    }

    public void setCodigoMatricula(Integer codigoMatricula) {
        this.codigoMatricula = codigoMatricula;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getChaveEmpresa() {
        return chaveEmpresa;
    }

    public void setChaveEmpresa(String chaveEmpresa) {
        this.chaveEmpresa = chaveEmpresa;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }
}
