package com.pacto.adm.core.dto.enveloperesposta.log.api;

import com.pacto.adm.core.dto.LogApiDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaLogApiDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private LogApiDTO content;

    public LogApiDTO getContent() {
        return content;
    }

    public void setContent(LogApiDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 12312, "
                    + "\"descricaoToken\": \"eyJ0eFBiOiJDV1QiLCJhbCciOiJIUzI1NiJ9\", "
                    + "\"dataUso\": \"2025-05-09T00:00:00Z\", "
                    + "\"ip\": \"*************\", "
                    + "\"method\": \"GET\", "
                    + "\"uri\": \"/empresas/12\", "
                    + "\"params\": \"codigo: 12\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";


}
