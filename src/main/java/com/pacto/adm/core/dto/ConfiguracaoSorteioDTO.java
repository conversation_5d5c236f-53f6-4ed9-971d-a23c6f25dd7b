package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;

@JsonInclude
@Schema(name = "Configuração do Sorteio", description = "Informações das configurações do sorteio")
public class ConfiguracaoSorteioDTO {

    @Schema(description = "Código identificador da configuração", example = "2")
    private Integer codigo;

    @Schema(description = "Planos vinculados a configuração do sorteio")
    private List<PlanoDTO> planos;

    @Schema(description = "Situações dos clientes que poderão participar do sorteio", example = "[AT, VI]")
    private List<String> situacoesCliente;

    @Schema(description = "Código identificador da empresa", example = "1")
    private Integer empresa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public List<PlanoDTO> getPlanos() {
        if (planos == null) {
            planos = new ArrayList<>();
        }
        return planos;
    }

    public void setPlanos(List<PlanoDTO> planos) {
        this.planos = planos;
    }

    public List<String> getSituacoesCliente() {
        if (situacoesCliente == null) {
            situacoesCliente = new ArrayList<>();
        }
        return situacoesCliente;
    }

    public void setSituacoesCliente(List<String> situacoesCliente) {
        this.situacoesCliente = situacoesCliente;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }
}
