package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Pacote Personal", description = "Pacote de Personal Trainer")
public class PacotePersonalDTO {


    @Schema(description = "Código único identificador do Pacote Personal", example = "120")
    private Integer codigo;

    @Schema(description = "Código do produto vinculado ao Pacote Personal", example = "398")
    private Integer produto;

    @Schema(description = "Quantidade de pacotes", example = "1")
    private Integer quantidade = 1;

    @Schema(description = "Valor Pós Pago do Pacote", example = "0")
    private Double valorPosPago = 0.0;

    @Schema(description = "Valor Pré Pago do Pacote", example = "100.00")
    private Double valorPrePago = 0.0;


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getProduto() {
        return produto;
    }

    public void setProduto(Integer produto) {
        this.produto = produto;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public Double getValorPosPago() {
        return valorPosPago;
    }

    public void setValorPosPago(Double valorPosPago) {
        this.valorPosPago = valorPosPago;
    }

    public Double getValorPrePago() {
        return valorPrePago;
    }

    public void setValorPrePago(Double valorPrePago) {
        this.valorPrePago = valorPrePago;
    }
}
