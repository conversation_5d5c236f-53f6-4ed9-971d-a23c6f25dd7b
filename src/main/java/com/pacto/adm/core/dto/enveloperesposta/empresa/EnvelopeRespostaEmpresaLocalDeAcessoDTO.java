package com.pacto.adm.core.dto.enveloperesposta.empresa;

import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.endereco.EnvelopeRespostaCidade;
import com.pacto.adm.core.dto.enveloperesposta.endereco.EnvelopeRespostaEstado;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaEmpresaLocalDeAcessoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private EmpresaDTO content;

    public EmpresaDTO getContent() {
        return content;
    }

    public void setContent(EmpresaDTO content) {
        this.content = content;
    }


    public static final String atributos =
            "\"codigo\": 160, "
                    + "\"nome\": \"Unidade Centro\", "
                    + "\"ativa\": true, "
                    + "\"setor\": \"Musculação\", "
                    + "\"estado\":  {" + EnvelopeRespostaEstado.atributos + "}, "
                    + "\"cidade\":  {" + EnvelopeRespostaCidade.atributos + "}";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
}
