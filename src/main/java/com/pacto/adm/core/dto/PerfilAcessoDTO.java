package com.pacto.adm.core.dto;

import com.pacto.adm.core.enumerador.PerfilUsuarioEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "Perfil de Acesso", description = "Informações do perfil de acesso ao sistema. Os dados cadastrados indicam as funções de cada colaborador ou usuário no sistema")
public class PerfilAcessoDTO {

    @Schema(description = "Código único identificador do perfil de acesso", example = "1")
    private Integer codigo;

    @Schema(description = "Nome do perfil de acesso", example = "PROFESSOR")
    private String nome;

    @Schema(
            description = "Tipo do perfil de acesso.<br/>" +
            "Tipos disponíveis" +
            "<ul>" +
                "<li>Todos</li>" +
                "<li>Administrador</li>" +
                "<li>Consultor</li>" +
                "<li>Gerente</li>" +
                "<li>Professor</li>" +
            "</ul>",
            example = "1",
            implementation = PerfilUsuarioEnum.class
    )
    private PerfilUsuarioEnum tipo;
}
