package com.pacto.adm.core.dto.filtros;

import org.json.JSONObject;

public class FiltroProdutoParceiroFidelidadeJSON {
    private String parametro;

    public FiltroProdutoParceiroFidelidadeJSON(JSONObject filters) {
        if (filters != null) {
            this.parametro = filters.optString("quickSearchValue").toUpperCase();
        }
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

}
