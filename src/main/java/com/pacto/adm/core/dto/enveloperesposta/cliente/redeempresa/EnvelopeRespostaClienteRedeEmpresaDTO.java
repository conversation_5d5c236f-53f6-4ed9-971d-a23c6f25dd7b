package com.pacto.adm.core.dto.enveloperesposta.cliente.redeempresa;

import com.pacto.adm.core.dto.ClienteRedeEmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;


@Schema(description = "Representação das respostas")
public class EnvelopeRespostaClienteRedeEmpresaDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ClienteRedeEmpresaDTO content;

    public ClienteRedeEmpresaDTO getContent() {
        return content;
    }

    public void setContent(ClienteRedeEmpresaDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 123, "
                    + "\"codigoMatricula\": 12303, "
                    + "\"nome\": \"<PERSON>lícia Stallone <PERSON>\", "
                    + "\"cpf\": \"234.567.891-10\", "
                    + "\"chaveEmpresa\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9\", "
                    + "\"codigoEmpresa\": 1, "
                    + "\"nomeEmpresa\": \"ACADEMIA PACTO\", "
                    + "\"dataSincronizacao\": \"2025-04-28T00:00:00.000Z\", "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";


}
