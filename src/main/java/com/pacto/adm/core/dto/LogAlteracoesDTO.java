package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class LogAlteracoesDTO {
    @Schema(description = "Nome do campo que teve alteração", example = "nome")
    private String campo;
    @Schema(description = "Valor anterior do campo", example = "Academiaa")
    private String valorAnterior;
    @Schema(description = "Valor do campo depois de alterado", example = "Academia")
    private String valorAlterado;

    public String getCampo() {
        return campo;
    }

    public void setCampo(String campo) {
        this.campo = campo;
    }

    public String getValorAnterior() {
        return valorAnterior;
    }

    public void setValorAnterior(String valorAnterior) {
        this.valorAnterior = valorAnterior;
    }

    public String getValorAlterado() {
        return valorAlterado;
    }

    public void setValorAlterado(String valorAlterado) {
        this.valorAlterado = valorAlterado;
    }
}
