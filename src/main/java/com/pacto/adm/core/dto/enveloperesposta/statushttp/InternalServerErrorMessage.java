package com.pacto.adm.core.dto.enveloperesposta.statushttp;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "InternalServer Message")
public class InternalServerErrorMessage {


    @Schema(description = "Código do erro conhecido", example = "validacao_negocio")
    private String error;

    @Schema(description = "Mensagem explicando o erro de negócio", example = "Não é possível realizar esta ação no momento.")
    private String message;

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
