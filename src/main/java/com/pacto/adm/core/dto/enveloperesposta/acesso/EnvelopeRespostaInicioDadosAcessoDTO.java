package com.pacto.adm.core.dto.enveloperesposta.acesso;

import com.pacto.adm.core.dto.InicioDadosAcessoDTO;
import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaLocalDeAcessoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Resposta Atestado", description = "Representação das respostas contendo as informações do atestado")
public class EnvelopeRespostaInicioDadosAcessoDTO extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private InicioDadosAcessoDTO content;

    public InicioDadosAcessoDTO getContent() {
        return content;
    }

    public void setContent(InicioDadosAcessoDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"listaEmpresas\": [{" + EnvelopeRespostaEmpresaLocalDeAcessoDTO.atributos + "}], "
                    + "\"listaLocalDeAcesso\": [{ "+ EnvelopeRespostaLocalDeAcessoDTO.atributos +"}], "
                    + "\"cliente\": { "+ EnvelopeRespostaClienteLocalDeAcessoDTO.atributos +" }, "
                    + "\"dataDeAcesso\": \"2025-04-28\", "
                    + "\"dataSaida\": \"2025-04-28\", "
                    + "\"horaEntradaRegistroAcesso\": \"06:33:23\", "
                    + "\"horaSaidaRegistroAcesso\": \"07:21:43\", "
                    + "\"meioIdentificacaoEntrada\": 1, "
                    + "\"meioIdentificacaoSaida\": 1, "
                    + "\"dataRegistro\": \"2025-04-28\", "
                    + "\"senhaAcesso\": \"abc1234\", "
                    + "\"confirmaSenhaAcesso\": \"abc1234\", "
                    + "\"habilitaSenhaAcesso\": false, "
                    + "\"registrarSaida\": false";

    public final static String resposta = "{\"content\": {" + atributos + "}}";

}
