package com.pacto.adm.core.dto.enveloperesposta.integracao.sesi;

import com.pacto.adm.core.dto.IntegracaoSesiDTO;
import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Respostas Aluguel de Armários", description = "Representação das respostas contendo uma lista de aluguel de armários")
public class EnvelopeRespostaListIntegracaoSesiDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private IntegracaoSesiDTO content;

    public IntegracaoSesiDTO getContent() {
        return content;
    }

    public void setContent(IntegracaoSesiDTO content) {
        this.content = content;
    }

    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaIntegracaoSesiDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";






}
