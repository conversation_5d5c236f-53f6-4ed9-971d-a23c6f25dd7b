package com.pacto.adm.core.dto;

import java.util.Date;

public class ClienteComFreepassDTO {

    private ClienteDTO cliente;
    private ContratoDTO contrato;
    private Date dataLancamento;
    private Integer qtdDiasFreepass;
    private UsuarioDTO responsavel;

    public ClienteComFreepassDTO(Date dataLancamento, Integer qtdDiasFreepass, ClienteDTO cliente, UsuarioDTO responsavel, ContratoDTO contrato) {
        this.dataLancamento = dataLancamento;
        this.qtdDiasFreepass = qtdDiasFreepass;
        this.cliente = cliente;
        this.responsavel = responsavel;
        this.contrato = contrato;
    }

    public ClienteDTO getCliente() {
        return cliente;
    }

    public ContratoDTO getContrato() {
        return contrato;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public Integer getQtdDiasFreepass() {
        return qtdDiasFreepass;
    }

    public UsuarioDTO getResponsavel() {
        return responsavel;
    }
}
