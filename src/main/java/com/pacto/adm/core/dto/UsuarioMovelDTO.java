package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.dto.colaborador.ColaboradorDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
public class UsuarioMovelDTO {

    @Schema(description = "Código único identificador do Usuário Móvel", example = "1")
    private Integer codigo;

    @Schema(description = "Nome do Usuário Móvel", example = "PACTO")
    private String nome;

    @Schema(description = "Senha do Usuário Móvel", example = "123456")
    private String senha;

    @Schema(description = "Indica se o usuário está ativo", example = "true")
    private Boolean ativo;

    @Schema(description = "Código único identificador do usuário no ZW", example = "1")
    private Integer usuarioZW;

    @Schema(description = "Código único identificador do usuário no TW", example = "1")
    private Integer usuarioTW;

    @Schema(description = "Código da empresa relacionada ao usuário", example = "1")
    private Integer empresa;

    @Schema(description = "Origem do usuário móvel", example = "PACTO")
    private String origem;

    @Schema(description = "CPF do Usuário Móvel", example = "123.456.768-10")
    private String cpf;

    @Schema(description = "Cliente relacionado ao Usuário Móvel")
    private ClienteDTO cliente;

    @Schema(description = "Colaborador relacionado ao Usuário Móvel")
    private ColaboradorDTO colaborador;

    public UsuarioMovelDTO() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getSenha() {
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public Integer getUsuarioZW() {
        return usuarioZW;
    }

    public void setUsuarioZW(Integer usuarioZW) {
        this.usuarioZW = usuarioZW;
    }

    public Integer getUsuarioTW() {
        return usuarioTW;
    }

    public void setUsuarioTW(Integer usuarioTW) {
        this.usuarioTW = usuarioTW;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }

    public ClienteDTO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteDTO cliente) {
        this.cliente = cliente;
    }

    public ColaboradorDTO getColaborador() {
        return colaborador;
    }

    public void setColaborador(ColaboradorDTO colaborador) {
        this.colaborador = colaborador;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }
}
