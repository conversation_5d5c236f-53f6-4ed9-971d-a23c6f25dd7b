package com.pacto.adm.core.dto.enveloperesposta.grupo;

import com.pacto.adm.core.entities.Grupo;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaGrupo {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private Grupo content;

    public Grupo getContent() {
        return content;
    }

    public void setContent(Grupo content) {
        this.content = content;
    }


    public static final String atributos =
            "\"codigo\": 22, "
                    + "\"descricao\": \"Grupo da Academia\", "
                    + "\"percentualDescontoGrupo\": 10.00, "
                    + "\"valorDescontoGrupo\": 50.00, "
                    + "\"tipoDesconto\": \"1\", "
                    + "\"quantidadeMinimaAluno\": 1, "
                    + "\"situacaoAluno\": \"AT\", "
                    + "\"tipo\": \"FR\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";
}
