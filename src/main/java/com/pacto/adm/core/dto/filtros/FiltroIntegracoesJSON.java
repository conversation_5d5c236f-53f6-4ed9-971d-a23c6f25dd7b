package com.pacto.adm.core.dto.filtros;

import com.pacto.adm.core.enumerador.ModuloEnum;
import com.pacto.config.exceptions.ServiceException;
import org.json.JSONObject;

public class FiltroIntegracoesJSON {
    private Integer codigoEmpresa;
    private ModuloEnum moduloEnum;

    public FiltroIntegracoesJSON(JSONObject filters) throws ServiceException {
        if (filters != null) {
            this.codigoEmpresa = filters.optInt("codigoEmpresa");
            if (!filters.optString("modulo").isEmpty()) {
                try {
                    moduloEnum = ModuloEnum.fromSigla(filters.optString("modulo"));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public ModuloEnum getModuloEnum() {
        return moduloEnum;
    }

    public void setModuloEnum(ModuloEnum moduloEnum) {
        this.moduloEnum = moduloEnum;
    }
}
