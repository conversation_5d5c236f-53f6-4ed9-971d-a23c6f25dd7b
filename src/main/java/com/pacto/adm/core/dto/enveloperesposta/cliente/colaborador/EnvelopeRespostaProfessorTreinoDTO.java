package com.pacto.adm.core.dto.enveloperesposta.cliente.colaborador;

import com.pacto.adm.core.dto.ProfessorTreinoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Resposta Informações Cliente Colaborador", description = "Representação das respostas contendo as informações do cliente visualizadas por um colaborador")
public class EnvelopeRespostaProfessorTreinoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ProfessorTreinoDTO content;

    public ProfessorTreinoDTO getContent() {
        return content;
    }

    public void setContent(ProfessorTreinoDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"nome\": \"Ronnie Coleman\", "
                    + "\"id\": 1, "
                    + "\"codigoPessoa\": 1230";

    public final static String resposta = "{\"content\": {" + atributos + "}}";


}
