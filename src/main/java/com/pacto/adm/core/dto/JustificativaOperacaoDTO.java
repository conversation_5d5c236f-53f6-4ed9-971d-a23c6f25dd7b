package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Justificativa da Operação", description = "Informações da justificativa da operação")
public class JustificativaOperacaoDTO {

    @Schema(description = "Código único identificador da justificativa de operação", example = "5867")
    private Integer codigo;

    @Schema(description = "Indica o tipo da operação" +
            "Os possíveis valores para o atributo são: " +
            "TS (TRANSFERENCIA_SAIDA), " +
            "AD (ALTERACAO_DURACAO), " +
            "BA (BONUS_ACRESCIMO), " +
            "BR (BONUS_REDUCAO), " +
            "CR (CARENCIA), " +
            "TE (TRANSFERENCIA_ENTRADA), " +
            "CA (CANCELAMENTO), " +
            "AH (ALTERACAO_HORARIO), " +
            "TR (TRANCAMENTO), " +
            "TV (TRANCAMENTO_VENCIDO), " +
            "RT (RETORNO_TRANCAMENTO), " +
            "IM (INCLUIR_MODALIDADE), " +
            "AM (ALTERAR_MODALIDADE), " +
            "EM (EXCLUIR_MODALIDADE), " +
            "AC (ALTERACAO_CONTRATO), " +
            "AT (ATESTADO), " +
            "RA (RETORNO_ATESTADO), " +
            "LV (LIBERAR_VAGA), " +
            "BC (BONUS_COLETIVO).", example = "AC")
    private String tipoOperacao;

    @Schema(description = "Descrição da operação", example = "Adição de atestado médico ao contrato")
    private String descricao;

    @Schema(description = "Empresa detentora da justificativa")
    private EmpresaDTO empresa;

    @Schema(description = "Indica se deve isentar a multa de cancelamento", example = "false")
    private Boolean isentarMultaCancelamento;

    @Schema(description = "Indica se não deve cobrar as parcelas atrasadas de cancelamento", example = "false")
    private Boolean naoCobrarParcelasAtrasadasCancelamento;

    @Schema(description = "Indica se é necessário anexar comprovante", example = "true")
    private Boolean necessarioAnexarComprovante;

    @Schema(description = "Indica se deve apresentar todas as empresas", example = "true")
    private Boolean apresentarTodasEmpresas;

    @Schema(description = "Indica se a justificativa está ativa", example = "true")
    private Boolean ativa;

    public JustificativaOperacaoDTO() {
    }

    public JustificativaOperacaoDTO(String descricao) {
        this.descricao = descricao;
    }

    public JustificativaOperacaoDTO(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getTipoOperacao() {
        return tipoOperacao;
    }

    public void setTipoOperacao(String tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public Boolean getIsentarMultaCancelamento() {
        return isentarMultaCancelamento;
    }

    public void setIsentarMultaCancelamento(Boolean isentarMultaCancelamento) {
        this.isentarMultaCancelamento = isentarMultaCancelamento;
    }

    public Boolean getNaoCobrarParcelasAtrasadasCancelamento() {
        return naoCobrarParcelasAtrasadasCancelamento;
    }

    public void setNaoCobrarParcelasAtrasadasCancelamento(Boolean naoCobrarParcelasAtrasadasCancelamento) {
        this.naoCobrarParcelasAtrasadasCancelamento = naoCobrarParcelasAtrasadasCancelamento;
    }

    public Boolean getNecessarioAnexarComprovante() {
        return necessarioAnexarComprovante;
    }

    public void setNecessarioAnexarComprovante(Boolean necessarioAnexarComprovante) {
        this.necessarioAnexarComprovante = necessarioAnexarComprovante;
    }

    public Boolean getApresentarTodasEmpresas() {
        return apresentarTodasEmpresas;
    }

    public void setApresentarTodasEmpresas(Boolean apresentarTodasEmpresas) {
        this.apresentarTodasEmpresas = apresentarTodasEmpresas;
    }

    public Boolean getAtiva() {
        return ativa;
    }

    public void setAtiva(Boolean ativa) {
        this.ativa = ativa;
    }
}
