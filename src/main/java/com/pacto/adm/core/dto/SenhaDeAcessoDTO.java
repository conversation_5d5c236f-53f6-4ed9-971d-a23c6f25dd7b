package com.pacto.adm.core.dto;

import com.pacto.adm.core.entities.Pessoa;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor

@Schema(name = "Senha de Acesso", description = "Informações da senha de acesso")
public class SenhaDeAcessoDTO {
    @Schema(description = "Código da matrícula do cliente utilizado para salvar a senha", example = "123")
    private Integer matricula;

    @Schema(description = "Dados do cliente que está criando a senha")
    private Pessoa pessoa;

    @Schema(description = "Senha de acesso que será salva", example = "abc1234")
    protected String senhaAcesso ;

    @Schema(description = "Confirmação da senha de acesso que será salva", example = "abc1234")
    protected String confirmaSenhaAcesso;

    @Schema(description = "Indica se a senha de acesso deve ser habilitada ou não", example = "false")
    protected Boolean habilitaSenhaAcesso = false;
}
