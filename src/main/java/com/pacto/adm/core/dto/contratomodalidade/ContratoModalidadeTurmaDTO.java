package com.pacto.adm.core.dto.contratomodalidade;

import com.pacto.adm.core.dto.turma.TurmaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.List;

@Schema(name = "ContratoModalidadeTurma", description = "Informações detalhadas sobre a turma vinculada à modalidade do contrato, incluindo presença, faltas e créditos.")
public class ContratoModalidadeTurmaDTO {

    @Schema(description = "Código único identificador da turma vinculada à modalidade.", example = "30")
    private Integer codigo;

    @Schema(description = "Detalhes da turma, incluindo nome, horários e instrutor responsável.")
    private TurmaDTO turma;

    @Schema(description = "Lista de horários associados à turma da modalidade do contrato.")
    private List<ContratoModalidadeHorarioTurmaDTO> horarios;

    @Schema(description = "Total de aulas desmarcadas no contrato anterior sem reposição.", example = "2")
    private Integer totalAulasDesmarcadasContratoPassado;

    @Schema(description = "Número total de presenças registradas na turma.", example = "15")
    private Integer presencas;

    @Schema(description = "Número total de faltas registradas na turma.", example = "3")
    private Integer faltas;

    @Schema(description = "Total de aulas realizadas pela turma.", example = "20")
    private Integer totalAulas;

    @Schema(description = "Quantidade total de créditos adquiridos para a turma.", example = "10")
    private Integer quantidadeCreditoCompra;

    @Schema(description = "Quantidade de créditos ainda disponíveis para a turma.", example = "4")
    private Integer quantidadeCreditoDisponivel;

    @Schema(description = "Data de início da matrícula na turma.", example = "2024-01-15T00:00:00Z")
    private Date dataInicioMatricula;

    @Schema(description = "Data de término da matrícula na turma.", example = "2024-12-15T00:00:00Z")
    private Date dataFimMatricula;

    @Schema(description = "Total de aulas desmarcadas sem reposição.", example = "1")
    private Integer totalAulasDesmarcadasSemReposicao;

    @Schema(description = "Total de aulas realizadas hoje pela turma.", example = "2")
    private Integer totalAulasHoje;

    @Schema(description = "Total de reposições em que o aluno esteve presente.", example = "3")
    private Integer totalReposicoesPresentes;

    @Schema(description = "Número total de aulas repostas na turma.", example = "5")
    private Integer reposicoes;

    @Schema(description = "Número total de aulas desmarcadas na turma.", example = "4")
    private Integer desmarcadas;

    public Integer getTotalAulasDesmarcadasSemReposicao() {
        return totalAulasDesmarcadasSemReposicao;
    }

    public void setTotalAulasDesmarcadasSemReposicao(Integer totalAulasDesmarcadasSemReposicao) {
        this.totalAulasDesmarcadasSemReposicao = totalAulasDesmarcadasSemReposicao;
    }

    public Integer getTotalAulasHoje() {
        return totalAulasHoje;
    }

    public void setTotalAulasHoje(Integer totalAulasHoje) {
        this.totalAulasHoje = totalAulasHoje;
    }

    public Date getDataInicioMatricula() {
        return dataInicioMatricula;
    }

    public void setDataInicioMatricula(Date dataInicioMatricula) {
        this.dataInicioMatricula = dataInicioMatricula;
    }

    public Date getDataFimMatricula() {
        return dataFimMatricula;
    }

    public void setDataFimMatricula(Date dataFimMatricula) {
        this.dataFimMatricula = dataFimMatricula;
    }

    public Integer getQuantidadeCreditoDisponivel() {
        return quantidadeCreditoDisponivel;
    }

    public Integer getTotalAulas() {
        return totalAulas;
    }

    public void setTotalAulas(Integer totalAulas) {
        this.totalAulas = totalAulas;
    }

    public void setQuantidadeCreditoDisponivel(Integer quantidadeCreditoDisponivel) {
        this.quantidadeCreditoDisponivel = quantidadeCreditoDisponivel;
    }

    public Integer getQuantidadeCreditoCompra() {
        return quantidadeCreditoCompra;
    }

    public void setQuantidadeCreditoCompra(Integer quantidadeCreditoCompra) {
        this.quantidadeCreditoCompra = quantidadeCreditoCompra;
    }


    public Integer getPresencas() {
        return presencas;
    }

    public void setPresencas(Integer presencas) {
        this.presencas = presencas;
    }

    public Integer getFaltas() {
        return faltas;
    }

    public void setFaltas(Integer faltas) {
        this.faltas = faltas;
    }

    public Integer getReposicoes() {
        return reposicoes;
    }

    public void setReposicoes(Integer reposicoes) {
        this.reposicoes = reposicoes;
    }

    public Integer getDesmarcadas() {
        return desmarcadas;
    }

    public void setDesmarcadas(Integer desmarcadas) {
        this.desmarcadas = desmarcadas;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public Integer getTotalAulasDesmarcadasContratoPassado() {
        return totalAulasDesmarcadasContratoPassado;
    }

    public void setTotalAulasDesmarcadasContratoPassado(Integer totalAulasDesmarcadasContratoPassado) {
        this.totalAulasDesmarcadasContratoPassado = totalAulasDesmarcadasContratoPassado;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public TurmaDTO getTurma() {
        return turma;
    }

    public void setTurma(TurmaDTO turma) {
        this.turma = turma;
    }

    public List<ContratoModalidadeHorarioTurmaDTO> getHorarios() {
        return horarios;
    }

    public void setHorarios(List<ContratoModalidadeHorarioTurmaDTO> horarios) {
        this.horarios = horarios;
    }

    public void setTotalReposicoesPresentes(Integer totalReposicoesPresentes) {
        this.totalReposicoesPresentes = totalReposicoesPresentes;
    }

    public Integer getTotalReposicoesPresentes() {
        return totalReposicoesPresentes;
    }

}
