package com.pacto.adm.core.dto.cliente;

import com.pacto.adm.core.dto.ClienteDTO;

public class ClienteAniversarianteDTO {
    private ClienteDTO cliente;
    private String nomePlano;
    private Integer duracaoContrato;

    public ClienteAniversarianteDTO(
            ClienteDTO cliente, String nomePlano, Integer duracaoContrato
    ) {
        this.cliente = cliente;
        this.nomePlano = nomePlano;
        this.duracaoContrato = duracaoContrato;
    }

    public String getNomePlano() {
        return nomePlano;
    }

    public void setNomePlano(String nomePlano) {
        this.nomePlano = nomePlano;
    }

    public Integer getDuracaoContrato() {
        return duracaoContrato;
    }

    public void setDuracaoContrato(Integer duracaoContrato) {
        this.duracaoContrato = duracaoContrato;
    }

    public ClienteDTO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteDTO cliente) {
        this.cliente = cliente;
    }
}
