package com.pacto.adm.core.dao.interfaces.solicitacaocompra;

import com.pacto.adm.core.dao.interfaces.DaoGenerico;
import com.pacto.adm.core.dto.filtros.FiltroSolicitacaoCompraJSON;
import com.pacto.adm.core.entities.solicitacaocompra.SolicitacaoCompra;
import com.pacto.config.dto.PaginadorDTO;

import java.util.List;

public interface SolicitacaoCompraDao extends DaoGenerico<SolicitacaoCompra, Integer> {

    List<SolicitacaoCompra> findAll(FiltroSolicitacaoCompraJSON filtros, PaginadorDTO paginadorDTO) throws Exception;
}
