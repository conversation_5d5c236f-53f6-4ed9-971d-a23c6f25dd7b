package com.pacto.adm.core.dao.interfaces;

import com.pacto.adm.core.entities.InfoMigracao;
import com.pacto.adm.core.enumerador.TipoInfoMigracaoEnum;

import java.util.List;

public interface InfoMigracaoDao extends DaoGenerico<InfoMigracao, Integer> {

    List<InfoMigracao> findByTipoInfoAndUsuario(Integer tipoInfo, Integer usuario, String username, String token, String chave) throws Exception;

    Boolean podeUsarNegociacao(Integer empresa) throws Exception;

    Boolean recursoPadraoEmpresa(TipoInfoMigracaoEnum recurso, Integer empresa) throws Exception;

    List<InfoMigracao> findAllHabilitadosByUsuario(Integer usuario, String username, String token, String chave) throws Exception;
}
