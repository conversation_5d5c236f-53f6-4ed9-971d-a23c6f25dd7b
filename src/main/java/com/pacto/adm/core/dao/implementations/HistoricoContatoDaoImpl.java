package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.HistoricoContatoDao;
import com.pacto.adm.core.entities.contato.HistoricoContato;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.UteisValidacao;
import org.hibernate.engine.spi.SessionImplementor;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

@Repository
public class HistoricoContatoDaoImpl extends DaoGenericoImpl<HistoricoContato, Integer> implements HistoricoContatoDao {

    @Override
    public boolean isFluxoGymBotJaUtilizado(int codigoFluxo, int tipoGymBot) throws ServiceException {
        List<Integer> codigo = new ArrayList<>();
        try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
            sessionImplementor.doWork(connection -> {
                String sql = "SELECT codigo FROM historicocontato h WHERE h.fluxogymbot = " + codigoFluxo + "AND h.tipoGymbotenum = "
                        + tipoGymBot + "LIMIT 1";
                try {
                    ResultSet rs = createStatement(connection, sql);
                    if (rs.next()) {
                        codigo.add(rs.getInt("codigo"));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    connection.close();
                }

            });
            if (UteisValidacao.emptyList(codigo)) {
                return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
        return true;
    }
}
