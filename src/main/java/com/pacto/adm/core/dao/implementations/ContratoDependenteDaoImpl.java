package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ContratoDependenteDao;
import com.pacto.adm.core.entities.contrato.ContratoDependente;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.utils.UteisValidacao;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class ContratoDependenteDaoImpl extends DaoGenericoImpl<ContratoDependente, Integer> implements ContratoDependenteDao {

    private static final int MAXIMO_RESULTADOS = 10;

    @Override
    public List<ContratoDependente> findAll(Integer codMatricula, Integer codContrato, Boolean somenteComCliente, PaginadorDTO paginadorDTO) throws Exception {
        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        getCurrentSession().clear();

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();
        where.append("WHERE 1 = 1 \n");

        if (!UteisValidacao.emptyNumber(codMatricula)) {
            where.append("AND obj.cliente.codigoMatricula = :codMatricula\n");
            params.put("codMatricula", codMatricula);
        }
        if (!UteisValidacao.emptyNumber(codContrato)) {
            where.append("AND obj.contrato.codigo = :codContrato\n");
            params.put("codContrato", codContrato);
        }
        if (somenteComCliente != null && somenteComCliente) {
            where.append("AND obj.cliente.codigo is not null \n");
        }

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParam("obj.codigo", where, params).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            if (paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                String sortField = paginadorDTO.getSort().split(",")[0];
                String sortOrder = paginadorDTO.getSort().split(",")[1];
                if (sortField.equalsIgnoreCase("dataFinalAjustada")) {
                    where.append(" order by obj.dataFinalAjustada " + sortOrder);
                } else if(sortField.equalsIgnoreCase("codigo")) {
                    where.append(" order by obj.codigo " + sortOrder);
                } else if(sortField.equalsIgnoreCase("dataInicio")) {
                    where.append(" order by obj.dataInicio " + sortOrder);
                } else if(sortField.equalsIgnoreCase("dataFinal")) {
                    where.append(" order by obj.dataFinal " + sortOrder);
                } else if(sortField.equalsIgnoreCase("posicaoDependente")) {
                    where.append(" order by obj.posicaoDependente " + sortOrder);
                } else if(sortField.equalsIgnoreCase("titular")) {
                    where.append(" order by obj.titular " + sortOrder);
                }
            } else {
                where.append(" order by obj.dataFinalAjustada desc");
            }
        } else {
            where.append(" order by obj.dataFinalAjustada desc");
        }

        return findByParam(where, params, maxResults, indiceInicial);
    }
}
