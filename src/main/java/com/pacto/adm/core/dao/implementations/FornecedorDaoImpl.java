package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.FornecedorDao;
import com.pacto.adm.core.entities.Fornecedor;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.Uteis;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class FornecedorDaoImpl extends DaoGenericoImpl<Fornecedor, Integer> implements FornecedorDao {
    private static final int MAXIMO_RESULTADOS = 50;

    @Override
    public List<Fornecedor> findAllByAtivo(PaginadorDTO paginadorDTO) throws Exception {
        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        getCurrentSession().clear();

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append("WHERE obj.dataValidade <> null and obj.dataValidade > '"+Uteis.getDataFormatoBD(Calendario.hoje())+"'");

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParam("obj.codigo", where, params).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            if (paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                String sortField = paginadorDTO.getSort().split(",")[0];
                String sortOrder = paginadorDTO.getSort().split(",")[1];
                if(sortField.equalsIgnoreCase("codigo")) {
                    where.append(" order by obj.codigo " + sortOrder);
                }
            }
        } else {
            where.append(" order by obj.pessoa.nome asc");
        }

        return findByParam(where, params, maxResults, indiceInicial);
    }
}
