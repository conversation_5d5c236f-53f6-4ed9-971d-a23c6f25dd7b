package com.pacto.adm.core.dao.interfaces;

import com.pacto.adm.core.dto.ClienteDadosGymPassDTO;
import com.pacto.adm.core.dto.ClienteDadosPessoaisDTO;
import com.pacto.adm.core.dto.ClienteInativoPeriodoAcessoDTO;
import com.pacto.adm.core.dto.ConfiguracaoSorteioDTO;
import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.dto.integracaomanychat.ClienteSimplificadoDTO;
import com.pacto.adm.core.dto.negociacao.ClienteNegociacaoDTO;
import com.pacto.adm.core.entities.Cliente;
import com.pacto.adm.core.entities.Produto;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;

public interface ClienteDao extends DaoGenerico<Cliente, Integer> {

    Cliente findByPessoa(Integer codigoPessoa) throws Exception;

    List<Cliente> findByNome(String nome) throws Exception;

    void alterarParqCliente(Cliente cliente) throws Exception;

    Cliente findByMatricula(Integer matricula) throws Exception;

    @Deprecated
    void salvaFreePass(Cliente cliente, ClienteDadosGymPassDTO clienteDadosGymPassDTO) throws Exception;

    void salvaFreePass(Cliente cliente) throws Exception;

    void salvarGymPass(Integer matricula, ClienteDadosGymPassDTO clienteDadosGymPassDTO) throws Exception;

    void deleteGymPass(Integer matricula) throws Exception;

    Cliente sortearCliente(ConfiguracaoSorteioDTO configuracaoSorteioDTO) throws Exception;

    void saveFreepass(Cliente cliente, Produto freepass, Integer usuarioResponsavelFreepass) throws Exception;

    List<ClienteNegociacaoDTO> consultarClientesSimplificado(String nome, Integer empresa) throws ServiceException;

    Double valorParcelasAberto(Integer codigoCliente) throws ServiceException;

    List<String> telefoneCliente(Integer codigoCliente) throws ServiceException;

    void salvaDadosChurn(ClienteDadosPessoaisDTO cliente) throws Exception;

    List<ClienteSimplificadoDTO> consultarClienteSimplificadoPorCpf(String cpf) throws Exception;

    boolean clienteEstaInadimplente(Integer codigoMatricula, Integer codigoEmpresa) throws ServiceException;

}
