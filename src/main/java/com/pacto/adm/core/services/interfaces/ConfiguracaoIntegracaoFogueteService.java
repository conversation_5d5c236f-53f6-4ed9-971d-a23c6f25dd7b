package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoFogueteDTO;
import com.pacto.config.exceptions.ServiceException;

public interface ConfiguracaoIntegracaoFogueteService {

    ConfiguracaoIntegracaoFogueteDTO findByEmpresaId(Integer codigoEmpresa) throws ServiceException;

    ConfiguracaoIntegracaoFogueteDTO salvar(ConfiguracaoIntegracaoFogueteDTO configuracaoIntegracaoGenericaLeadsDTO) throws ServiceException;

}
