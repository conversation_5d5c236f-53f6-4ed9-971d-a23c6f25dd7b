package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.ClienteMensagemDTO;
import com.pacto.adm.core.dto.filtros.FiltroClienteMensagemJSON;
import com.pacto.adm.core.entities.Produto;
import com.pacto.adm.core.entities.Usuario;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;

import java.util.Date;
import java.util.List;

public interface ClienteMensagemService {

    void processarProdutoAtestado(Integer cliente, Produto produto, Date finalVigencia, Usuario usuario) throws Exception;

    void excluirClienteMensagemPorMovParcela(Integer codMovParcela) throws Exception;

    List<ClienteMensagemDTO> findAllByCodPessoa(Integer codPessoa, FiltroClienteMensagemJSON filtroJSON,
                                                PaginadorDTO paginadorDTO) throws ServiceException;
    void excluir(Integer codigo) throws Exception;

    ClienteMensagemDTO obter(Integer codigo) throws Exception;

    ClienteMensagemDTO saveOrUpdate(ClienteMensagemDTO dto) throws ServiceException;
}
