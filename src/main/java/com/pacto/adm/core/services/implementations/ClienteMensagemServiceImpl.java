package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.ClienteMensagemAdapter;
import com.pacto.adm.core.dao.interfaces.ClienteMensagemDao;
import com.pacto.adm.core.dao.interfaces.ClienteRepository;
import com.pacto.adm.core.dto.ClienteMensagemDTO;
import com.pacto.adm.core.dto.filtros.FiltroClienteMensagemJSON;
import com.pacto.adm.core.entities.ClienteMensagem;
import com.pacto.adm.core.entities.Produto;
import com.pacto.adm.core.entities.Usuario;
import com.pacto.adm.core.enumerador.TiposMensagensEnum;
import com.pacto.adm.core.services.interfaces.ClienteMensagemService;
import com.pacto.adm.core.services.interfaces.LogService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.Uteis;
import org.hibernate.engine.spi.SessionImplementor;
import org.hibernate.exception.ConstraintViolationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.util.Date;
import java.util.List;
import java.util.Locale;

@Service
public class ClienteMensagemServiceImpl implements ClienteMensagemService {

    @Autowired
    private ClienteMensagemDao clienteMensagemDao;
    @Autowired
    private ClienteMensagemAdapter clienteMensagemAdapter;
    @Autowired
    private MessageSource messageSource;
    @Autowired
    private RequestService requestService;
    @Autowired
    private LogService logService;
    @Autowired
    private ClienteRepository clienteRepository;

    @Override
    public void processarProdutoAtestado(Integer cliente, Produto produto, Date finalVigencia, Usuario usuario) throws Exception {
        Boolean vencido = finalVigencia != null && Calendario.menor(finalVigencia, Calendario.hoje());
        ClienteMensagem mensagem = clienteMensagemDao.consultarObjClienteMensagemPorProdutoVencido(cliente, produto.getCodigo());
        if (!vencido && mensagem != null) {
            clienteMensagemDao.delete(mensagem);
        } else if (vencido && mensagem == null) {
            ClienteMensagem msg = new ClienteMensagem();
            msg.getCliente().setCodigo(cliente);
            msg.setMensagem(TiposMensagensEnum.PRODUTO_VENCIDO.getMensagem().replace("Z", produto.getDescricao()).replace("xx/xx/xx",
                    Uteis.getData(finalVigencia, "br")));
            msg.setTipoMensagem(TiposMensagensEnum.PRODUTO_VENCIDO.getSigla());
            msg.getUsuario().setCodigo(usuario.getCodigo());
            msg.getProduto().setCodigo(produto.getCodigo());
            msg.setBloqueio(produto.getPrevalecerVigenciaContrato());
            clienteMensagemDao.save(msg);
        }
    }

    @Override
    public void excluirClienteMensagemPorMovParcela(Integer codMovParcela) throws Exception {
        clienteMensagemDao.getCurrentSession().clear();

        String sql = "SELECT codigo FROM ClienteMensagem WHERE movParcela = " + codMovParcela;

        try (SessionImplementor sessionImplementor = clienteMensagemDao.createSessionCurrentWork()) {
            sessionImplementor.doWork(connection -> {
                try {
                    ResultSet rs = clienteMensagemDao.createStatement(connection, sql);
                    while (rs.next()) {
                        clienteMensagemDao.delete(rs.getInt("codigo"));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    connection.close();
                }
            });
        }
    }

    public List<ClienteMensagemDTO> findAllByCodPessoa(Integer codPessoa,
                                                       FiltroClienteMensagemJSON filtroJSON,
                                                       PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<ClienteMensagem> lista = clienteMensagemDao.findAllByPessoa(codPessoa, filtroJSON, paginadorDTO);
            return clienteMensagemAdapter.toDtos(lista);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void excluir(Integer codigo) throws Exception {
        clienteMensagemDao.getCurrentSession().clear();
        ClienteMensagem obj = clienteMensagemDao.findById(codigo);
        if (obj == null) {
            throw new ServiceException(messageSource.getMessage("cliente.mensagem.nao.encontrado", null, new Locale(requestService.getLocale())));
        }
        clienteMensagemDao.delete(obj);

        try {
            String nomeEntidade = getNomeEntidadeLog(obj);
            if (nomeEntidade != null) {
                Integer pessoaCliente = clienteRepository.findCodPessoaByCodCliente(obj.getCliente().getCodigo());
                logService.incluirLogExcluscao(obj, nomeEntidade, "ClienteMensagemVO", pessoaCliente);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public ClienteMensagemDTO obter(Integer codigo) throws Exception {
        clienteMensagemDao.getCurrentSession().clear();
        ClienteMensagem obj = clienteMensagemDao.findById(codigo);
        return clienteMensagemAdapter.toDto(obj);
    }

    @Override
    public ClienteMensagemDTO saveOrUpdate(ClienteMensagemDTO dto) throws ServiceException {
        try {
            clienteMensagemDao.getCurrentSession().clear();

            ClienteMensagem obj;
            ClienteMensagem objExiste = null;
            if (Uteis.intNullOrEmpty(dto.getCodigo())) {
                obj = clienteMensagemAdapter.toEntity(dto);
                obj.setCodigo(null);
                obj.setDataRegistro(new Date());
                if (obj.getDesabilitado() == null) {
                    obj.setDesabilitado(false);
                }
                obj = clienteMensagemDao.save(obj);
            } else {
                objExiste = clienteMensagemDao.findById(dto.getCodigo());
                obj = clienteMensagemAdapter.toEntity(dto);
                obj.setCodigo(objExiste.getCodigo());
                obj.setDataRegistro(objExiste.getDataRegistro());
                obj.setDataAtualizacao(new Date());
                obj = clienteMensagemDao.update(obj);
            }
            try {
                String nomeEntidade = getNomeEntidadeLog(obj);
                if (nomeEntidade != null) {
                    Integer pessoaCliente = clienteRepository.findCodPessoaByCodCliente(obj.getCliente().getCodigo());
                    logService.incluirLogInclusaoAlteracao(obj, objExiste, nomeEntidade, "ClienteMensagemVO", pessoaCliente);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            return clienteMensagemAdapter.toDto(obj);
        } catch (Exception e) {
            e.printStackTrace();
            if (e.getCause() instanceof ConstraintViolationException) {
                org.hibernate.exception.ConstraintViolationException constraintViolationException = ((org.hibernate.exception.ConstraintViolationException) e.getCause());
                throw new ServiceException(constraintViolationException.getSQLException());
            }
            throw new ServiceException(e);
        }
    }

    private String getNomeEntidadeLog(ClienteMensagem clienteMensagem) {
        if (clienteMensagem.getTipoMensagem().equalsIgnoreCase(TiposMensagensEnum.AVISO_CONSULTOR.getSigla())) {
            return "MENSAGEMCONSULTOR";
        }
        return null;
    }
}
