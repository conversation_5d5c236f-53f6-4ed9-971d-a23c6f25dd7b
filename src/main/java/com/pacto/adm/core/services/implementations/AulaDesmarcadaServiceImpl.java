package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.dao.interfaces.ControleCreditoTreinoDao;
import com.pacto.adm.core.dto.ClienteDadosPlanoDTO;
import com.pacto.adm.core.dto.VinculoDTO;
import com.pacto.adm.core.dto.auladesmarcada.ContadorReposicoesDTO;
import com.pacto.adm.core.dto.auladesmarcada.ReposicaoAulaColetivaDTO;
import com.pacto.adm.core.enumerador.SituacaoDoContratoEnum;
import com.pacto.adm.core.services.interfaces.AulaDesmarcadaService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.hibernate.engine.spi.SessionImplementor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;


@Service
public class AulaDesmarcadaServiceImpl implements AulaDesmarcadaService {

    private static final int MAXIMO_RESULTADOS = 10;

    @Autowired
    private ControleCreditoTreinoDao controleCreditoTreinoDao;

    @Override
    public Integer contarAulasDesmarcadasPorPeriodo(int contrato, int horarioTurma, Date datainicio, Date datafim) {
        return contarAulasDesmarcadasPorPeriodo(contrato, horarioTurma, datainicio, datafim, true);
    }

    @Override
    public void excluirAulaDesmarcadaPorRetornoAfastamentoAntecipado(Integer codigoContrato, Date dataRetorno, Date dataFimAfastamento) throws Exception {
        try (SessionImplementor sessionImplementor = controleCreditoTreinoDao.createSessionCurrentWork()){
            sessionImplementor.doWork(con -> {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                    Date dataFim = Calendario.getDataComHora(dataFimAfastamento, "23:59");
                    // Excluir os registros de controle de crédito.
                    StringBuilder sql = new StringBuilder();
                    sql.append("delete from controleCreditoTreino \n");
                    sql.append("where aulaDesmarcada in( \n");
                    sql.append("        select ad.codigo \n");
                    sql.append("        from auladesmarcada ad \n");
                    sql.append("        inner join horarioTurma ht on ht.codigo = ad.horarioTurma \n");
                    sql.append("        where ad.desmarcadaPorAfastamento =true and ad.contrato = ").append(codigoContrato);
                    sql.append("        and cast(cast (ad.dataOrigem as varchar(10)) || ' ' ||  ht.horaFinal as timestamp) between '").append(sdf.format(dataRetorno)).append("' and '").append(sdf.format(dataFim)).append("' \n");
                    sql.append("        )");
                    Statement stExcluirCredito = con.createStatement();
                    stExcluirCredito.execute(sql.toString());

                    sql.delete(0, sql.length());
                    sql.append("delete from auladesmarcada ad \n");
                    sql.append("using horarioTurma ht \n");
                    sql.append("where desmarcadaPorAfastamento = true \n");
                    sql.append("and ad.horarioTurma = ht.codigo \n");
                    sql.append("and ad.contrato = ").append(codigoContrato);
                    sql.append(" and cast(cast (ad.dataOrigem as varchar(10)) || ' ' ||  ht.horaFinal as timestamp) between '").append(sdf.format(dataRetorno)).append("' and '").append(sdf.format(dataFim)).append("' \n");
                    Statement stExcluirAula = con.createStatement();
                    stExcluirAula.execute(sql.toString());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    con.close();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Integer contarAulasDesmarcadasPorPeriodo(int contrato, int horarioTurma, Date datainicio, Date datafim, boolean permiteReporAulaDesmarcada) {
        AtomicReference<Integer> total = new AtomicReference<>(0);
        try (SessionImplementor sessionImplementor = controleCreditoTreinoDao.createSessionCurrentWork()) {
            sessionImplementor.doWork(con -> {
                try {
                    StringBuilder sql = new StringBuilder("select count(codigo) as aulas from auladesmarcada ");
                    sql.append(" where permiteReporAulaDesmarcada = ").append(permiteReporAulaDesmarcada).append(" and reposicao is null ");
                    if (!UteisValidacao.emptyNumber(contrato)) {
                        sql.append(" and contrato = ?  ");
                    }
                    if (!UteisValidacao.emptyNumber(horarioTurma)) {
                        sql.append(" and horarioturma = ? ");
                    }
                    if (datainicio != null) {
                        sql.append(" and dataorigem::date >= ? ");
                    }
                    if (datafim != null) {
                        sql.append(" and dataorigem::date <= ? ");
                    }
                    sql.append(" and contratoanterior is null");

                    try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
                        int i = 0;
                        if (!UteisValidacao.emptyNumber(contrato)) {
                            pst.setInt(++i, contrato);
                        }
                        if (!UteisValidacao.emptyNumber(horarioTurma)) {
                            pst.setInt(++i, horarioTurma);
                        }
                        if (datainicio != null) {
                            pst.setDate(++i, Uteis.getDataJDBC(datainicio));
                        }
                        if (datafim != null) {
                            pst.setDate(++i, Uteis.getDataJDBC(datafim));
                        }

                        ResultSet tabelaResultado = pst.executeQuery();
                        if (tabelaResultado.next()) {
                            total.set(tabelaResultado.getInt("aulas"));
                        }
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    con.close();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        return total.get();
    }

    public List<ReposicaoAulaColetivaDTO> reposicoesAulaColetiva(Integer matricula, PaginadorDTO paginadorDTO) throws ServiceException{
        try {
            List<ReposicaoAulaColetivaDTO> dados = new ArrayList<ReposicaoAulaColetivaDTO>();
            try (SessionImplementor sessionImplementor = controleCreditoTreinoDao.createSessionCurrentWork()) {
                sessionImplementor.doWork(connection -> {
                    try {

                        int maxResults = MAXIMO_RESULTADOS;
                        int indiceInicial = 0;
                        StringBuilder sql = new StringBuilder();
                        sql.append("select a.datalimitereposicao, a.diareposta, a.aulareposta, a.contrato,\n");
                        sql.append(" m.nome as modalidade,\n");
                        sql.append(" a.dia, a.lancamento,\n");
                        sql.append(" a.manternarenovacao,\n");
                        sql.append(" con.vigenciaateajustada from alunohorarioturmadesmarcado a\n");
                        sql.append(" inner join cliente c on a.cliente = c.codigo \n");
                        sql.append(" inner join horarioturma h on h.codigo = a.horarioturma \n");
                        sql.append(" inner join turma t on t.codigo = h.turma \n");
                        sql.append(" inner join modalidade m on m.codigo = t.modalidade \n");
                        sql.append(" inner join contrato con on con.codigo = a.contrato \n");
                        sql.append(" where a.reposicao and c.codigomatricula = " ).append(matricula);
                        sql.append(" order by a.codigo desc ");
                        if (paginadorDTO != null) {
                            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
                            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
                            if (paginadorDTO.getPage() != null) {
                                sql.append(" offset " ).append(paginadorDTO.getPage());
                            }
                            if (paginadorDTO.getSize() != null) {
                                sql.append(" limit " ).append(paginadorDTO.getSize());
                            }
                        }
                        ResultSet reposicoesRs = controleCreditoTreinoDao.createStatement(connection, sql.toString());
                        while (reposicoesRs.next()) {
                            ReposicaoAulaColetivaDTO reposicao = new ReposicaoAulaColetivaDTO();
                            reposicao.setContrato(reposicoesRs.getInt("contrato"));
                            reposicao.setModalidade(reposicoesRs.getString("modalidade"));
                            reposicao.setData(Uteis.getData(reposicoesRs.getDate("dia")));
                            Date diareposta = reposicoesRs.getDate("diareposta");
                            reposicao.setDataReposta(diareposta != null ? Uteis.getData(diareposta) : "-");
                            int aulareposta = reposicoesRs.getInt("aulareposta");
                            Date dataLimite = reposicoesRs.getDate("datalimitereposicao" );
                            reposicao.setLimiteParaResposicao(Uteis.getData(dataLimite));
                            if(aulareposta > 0){
                                reposicao.setReposicao("Utilizada");
                                reposicao.setMotivo("Reposição feita");
                            } else if(Calendario.maiorOuIgual( dataLimite, Calendario.hoje())){
                                reposicao.setReposicao("Disponível");
                                reposicao.setMotivo("Cancelamento aula");
                            } else {
                                reposicao.setReposicao("Expirada");
                                reposicao.setMotivo("Reposição expirada");
                            }
                            dados.add(reposicao);
                        }
                        if (paginadorDTO != null) {
                            paginadorDTO.setQuantidadeTotalElementos(contagemReposicoesAulaColetiva(matricula,connection).longValue());
                            paginadorDTO.setSize((long) maxResults);
                            paginadorDTO.setPage((long) indiceInicial);
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    } finally {
                        connection.close();
                    }
                });
            }
            return dados;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    public Integer contagemReposicoesAulaColetiva(Integer matricula, Connection con) throws Exception {
        ResultSet cont = controleCreditoTreinoDao.createStatement(con,
                "select count(*) as total from alunohorarioturmadesmarcado a\n" +
                " inner join cliente c on a.cliente = c.codigo\n" +
                " where a.reposicao and c.codigomatricula = " + matricula);
        return cont.next() ? cont.getInt("total") : 0;
    }

    public ContadorReposicoesDTO contagemReposicoesAulaColetiva(Integer matricula) throws ServiceException {
        try {
            ContadorReposicoesDTO dados = new ContadorReposicoesDTO();
            String sql = "select count(*) as total from alunohorarioturmadesmarcado a\n" +
                    " inner join cliente c on a.cliente = c.codigo\n" +
                    " inner join contrato con on con.codigo = a.contrato \n" +
                    " inner join plano p on p.codigo = con.plano\n" +
                    " where a.reposicao and c.codigomatricula = " + matricula +
                    " and aulareposta is null ";
            try (SessionImplementor sessionImplementor = controleCreditoTreinoDao.createSessionCurrentWork()) {
                sessionImplementor.doWork(connection -> {
                    try {
                        ResultSet contDisponiveis = controleCreditoTreinoDao.createStatement(connection, sql +
                                " and datalimitereposicao >= current_date\n" +
                                " and (con.vigenciaateajustada >= current_date or a.manternarenovacao)");
                        if (contDisponiveis.next()) {
                            dados.setReposicoesDisponiveis(contDisponiveis.getInt("total"));
                        }
                        ResultSet contExpiradas = controleCreditoTreinoDao.createStatement(connection, sql +
                                " and (datalimitereposicao  < current_date\n" +
                                " or (con.vigenciaateajustada < current_date and not a.manternarenovacao))");
                        if (contExpiradas.next()) {
                            dados.setReposicoesExpiradas(contExpiradas.getInt("total"));
                        }
                        ResultSet contUtilizadas = controleCreditoTreinoDao.createStatement(connection, "select count(*) as total from alunohorarioturmadesmarcado a \n" +
                                "inner join cliente c on a.cliente = c.codigo \n" +
                                "inner join contrato con on a.contrato = con.codigo \n" +
                                "where a.reposicao and c.codigomatricula = "+ matricula + "\n" +
                                "and aulareposta is not null" );
                        if (contUtilizadas.next()) {
                            dados.setReposicoesUtilizadas(contUtilizadas.getInt("total"));
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    } finally {
                        connection.close();
                    }
                });
            }
            return dados;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }
}
