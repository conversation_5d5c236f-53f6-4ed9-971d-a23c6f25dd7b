package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.dao.interfaces.HistoricoContratoDao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.entities.contrato.HistoricoContrato;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.adm.core.services.interfaces.ValidacaoHistoricoContratoService;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.Uteis;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class ValidacaoHistoricoContratoServiceImpl implements ValidacaoHistoricoContratoService {

    @Autowired
    private HistoricoContratoDao historicoContratoDao;

    /**
     * Verifica se a partir de uma dada operação com dataInicio e fim, vai chocar
     * com um histórico de contrato já existente.
     */
    @Override
    public void validarPeriodoHistoricoContratoOperacao(Date dataInicio, Date dataFim, int codigoContrato, HistoricoContrato historicoNovo) throws Exception {
        /**
         * Não validar se a operação é retroativa, pois esta não mudará o histórico de contrato,
         * exceto para Cancelamento
         */
        if (Calendario.menor(dataInicio, Calendario.getDataComHoraZerada(Calendario.hoje()))) {
            if (historicoNovo.getTipoHistorico().equals("CA")) {
                List<HistoricoContrato> lista = historicoContratoDao.findAllByContrato(codigoContrato, new PaginadorDTO());
                for (HistoricoContrato historicoContratoVO : lista) {
                    //verificar se existe um histórico de trancamento ou retorno de trancamento depois da data de cancelamento
                    if (historicoContratoVO.getTipoHistorico().equals("TR")) {
                        if (Calendario.menorOuIgual(historicoNovo.getDataInicioSituacao(), historicoContratoVO.getDataInicioSituacao())) {
                            throw new Exception(String.format(
                                    "Esta Operação não é suportada. "
                                            + "A data de um Cancelamento não pode anteceder ou começar no mesmo dia de uma operação de: \"%s\".",
                                    new Object[]{
                                            historicoContratoVO.getDescricao()
                                    }));
                        }
                    } else if (historicoContratoVO.getTipoHistorico().equals("RT")) {
                        if (Calendario.menor(historicoNovo.getDataInicioSituacao(), historicoContratoVO.getDataInicioSituacao())) {
                            throw new Exception(String.format(
                                    "Esta Operação não é suportada. "
                                            + "A data de um Cancelamento não pode anteceder uma operação de: \"%s\".",
                                    new Object[]{
                                            historicoContratoVO.getDescricao()
                                    }));
                        }
                    }
                }
            }
            return;
        }
        if (codigoContrato != 0) {
            List<HistoricoContrato> lista = historicoContratoDao.findAllByContrato(codigoContrato, new PaginadorDTO());
            for (HistoricoContrato historicoContrato : lista) {
                //historico a vencer não deve ser considerado nessa validação
                if (historicoContrato.getTipoHistorico().equals("AV")) {
                    continue;
                }

                if (!historicoNovo.getTipoHistorico().equals("CA")) {
                    if (Calendario.igual(dataInicio, historicoContrato.getDataInicioSituacao())) {
                        throw new ServiceException(String.format(
                                "Já existe um histórico de contrato \"%s\" para o contrato \"%s\" com a mesma data de início \"%s\".",
                                new Object[]{
                                        historicoContrato.getDescricao(),
                                        historicoContrato.getContrato().getCodigo().toString(),
                                        Uteis.getData(historicoContrato.getDataInicioSituacao())
                                }));
                    }
                    if (Calendario.igual(dataFim, historicoContrato.getDataFinalSituacao())) {
                        throw new ServiceException(String.format(
                                "Já existe um histórico de contrato \"%s\" para o contrato \"%s\" com a mesma data final \"%s\".",
                                new Object[]{
                                        historicoContrato.getDescricao(),
                                        historicoContrato.getContrato().getCodigo().toString(),
                                        Uteis.getData(historicoContrato.getDataFinalSituacao())
                                }));
                    }
                }

                if ( (historicoContrato.getDataInicioSituacao() != null && historicoContrato.getDataFinalSituacao() != null )) {
                    if(Calendario.entre(dataInicio,
                            historicoContrato.getDataInicioSituacao(), historicoContrato.getDataFinalSituacao())
                            || Calendario.entre(dataFim,
                            historicoContrato.getDataInicioSituacao(), historicoContrato.getDataFinalSituacao())) {
                        throw new ServiceException(String.format(
                                "Já existe um histórico de contrato \"%s\" para o contrato \"%s\" no período de \"%s\" até \"%s\".",
                                new Object[]{
                                        historicoContrato.getDescricao(),
                                        historicoContrato.getContrato().getCodigo().toString(),
                                        Uteis.getData(historicoContrato.getDataInicioSituacao()),
                                        Uteis.getData(historicoContrato.getDataFinalSituacao())
                                }));
                    }

                }


            }

        }
    }
}
