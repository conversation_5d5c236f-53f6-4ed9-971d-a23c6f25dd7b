package com.pacto.adm.core.services.implementations.estornoobservacao;

import com.pacto.adm.core.dao.interfaces.nativerepositories.estornoobservacao.EstornoObservacaoNativeRepository;
import com.pacto.adm.core.dto.estornoobservacao.EstornoObservacaoDTO;
import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.services.interfaces.estornoobservacao.EstornoObservacaoService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class EstornoObservacaoServiceImpl implements EstornoObservacaoService {

    private final EstornoObservacaoNativeRepository estornoObservacaoNativeRepository;

    public EstornoObservacaoServiceImpl(EstornoObservacaoNativeRepository estornoObservacaoNativeRepository) {
        this.estornoObservacaoNativeRepository = estornoObservacaoNativeRepository;
    }

    @Override
    public List<EstornoObservacaoDTO> consultarPorNomeEntidadePorDataAlteracaoPorOperacao(FiltroBIControleOperacoesJSON filtros, PaginadorDTO paginadorDTO, boolean buscarComAdministrador, boolean buscarComRecorrencia) throws ServiceException {

        if (filtros.getInicio() == null) {
            throw new ServiceException("filtro-data-inicio-nao-informado", "Deve ser informado o filtro data início para consulta!");
        }

        if (filtros.getFim() == null) {
            throw new ServiceException("filtro-data-fim-nao-informado", "Deve ser informado o filtro data fim para consulta!");
        }

        try {
            return estornoObservacaoNativeRepository.consultarPorNomeEntidadePorDataAlteracaoPorOperacao(
                    filtros, paginadorDTO, buscarComAdministrador, buscarComRecorrencia
            );
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("erro-consulta-contratos-estornados", "Erro ao consultar os constratos estornados!");
        }
    }
}
