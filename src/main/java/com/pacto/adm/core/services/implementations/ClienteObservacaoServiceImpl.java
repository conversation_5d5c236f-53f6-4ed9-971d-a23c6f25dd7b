package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.ClienteObservacaoAdapter;
import com.pacto.adm.core.dao.interfaces.ClienteObservacaoDao;
import com.pacto.adm.core.dao.interfaces.PessoaDao;
import com.pacto.adm.core.dto.ClienteDTO;
import com.pacto.adm.core.dto.ClienteObservacaoDTO;
import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.dto.base.ClientDiscoveryDataDTO;
import com.pacto.adm.core.entities.ClienteObservacao;
import com.pacto.adm.core.services.interfaces.ClienteObservacaoService;
import com.pacto.adm.core.services.interfaces.DiscoveryService;
import com.pacto.adm.core.services.interfaces.LogService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.config.utils.*;
import com.pacto.treino.dao.ClienteObservacaoTreinoRepository;
import com.pacto.treino.entities.ClienteObservacaoTreino;
import org.hibernate.exception.ConstraintViolationException;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

@Service
public class ClienteObservacaoServiceImpl implements ClienteObservacaoService {

    @Autowired
    private MessageSource messageSource;
    @Autowired
    private RequestService requestService;
    @Autowired
    private ClienteObservacaoDao clienteObservacaoDao;
    @Autowired
    private ClienteObservacaoTreinoRepository clienteObservacaoTreinoRepository;
    @Autowired
    private PessoaDao pessoaDao;
    @Autowired
    private ClienteObservacaoAdapter clienteObservacaoAdapter;
    @Autowired
    private LogService logService;
    @Autowired
    private HttpServico httpServico;
    @Autowired
    private DiscoveryService discoveryService;

    public List<ClienteObservacaoDTO> findAllByCodPessoa(Integer codPessoa, JSONObject filtros,
                                                         PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<ClienteObservacao> lista = clienteObservacaoDao.findAllByPessoa(codPessoa, filtros, paginadorDTO);
            return clienteObservacaoAdapter.toDtos(lista);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Integer totalByCodPessoa(Integer codPessoa) throws ServiceException {
        try {
            return clienteObservacaoDao.totalByPessoa(codPessoa);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ClienteObservacaoDTO saveOrUpdate(ClienteObservacaoDTO dto, Integer codigo) throws ServiceException {
        try {
            validacoes(dto);
            clienteObservacaoDao.getCurrentSession().clear();

            ClienteObservacao obj;
            if (Uteis.intNullOrEmpty(codigo)) {
                obj = clienteObservacaoAdapter.toEntity(dto);
                obj.setCodigo(null);
                obj.setDataCadastro(new Date());
                obj = clienteObservacaoDao.save(obj);
                logService.incluirLogClienteObservacao(null, obj, pessoaDao.findByCliente(obj.getCliente().getCodigo()), false);
            } else {
                ClienteObservacao objAnterior = clienteObservacaoDao.findById(codigo);
                obj = clienteObservacaoAdapter.toEntity(dto);
                obj.setCodigo(objAnterior.getCodigo());
                obj.setDataAlteracao(new Date());
                obj = clienteObservacaoDao.update(obj);
                logService.incluirLogClienteObservacao(objAnterior, obj, pessoaDao.findByCliente(obj.getCliente().getCodigo()), false);
            }
            return clienteObservacaoAdapter.toDto(obj);
        } catch (Exception e) {
            e.printStackTrace();
            if (e.getCause() instanceof ConstraintViolationException) {
                org.hibernate.exception.ConstraintViolationException constraintViolationException = ((org.hibernate.exception.ConstraintViolationException) e.getCause());
                throw new ServiceException(constraintViolationException.getSQLException());
            }
            throw new ServiceException(e);
        }
    }

    @Override
    public void excluir(Integer codigo) throws Exception {
        clienteObservacaoDao.getCurrentSession().clear();
        ClienteObservacao obj = clienteObservacaoDao.findById(codigo);
        clienteObservacaoDao.delete(obj);
        logService.incluirLogClienteObservacao(null, obj, pessoaDao.findByCliente(obj.getCliente().getCodigo()), true);
    }

    @Override
    public ClienteObservacaoDTO obter(Integer codigo) throws Exception {
        clienteObservacaoDao.getCurrentSession().clear();
        ClienteObservacao obj = clienteObservacaoDao.findById(codigo);
        return clienteObservacaoAdapter.toDto(obj);
    }

    @Override
    public List<ClienteObservacaoDTO> findAllByCodMatricula(Integer codMatricula) throws ServiceException {
        try {
            List<ClienteObservacaoDTO> listaGeral = new ArrayList<>();
            List<ClienteObservacao> lista = clienteObservacaoDao.findAllByMatricula(codMatricula);
            List<ClienteObservacaoDTO> listaZW = clienteObservacaoAdapter.toDtos(lista);
            listaZW.forEach(dto -> {
                dto.setTipo("v2");
            });
            listaGeral.addAll(listaZW);
            List<ClienteObservacaoDTO> listaTR = obterObservacoesTreino(codMatricula + "");
            listaTR.forEach(dto -> {
                dto.setTipo("v1");
            });
            listaGeral.addAll(listaTR);

            Ordenacao.ordenarListaReverse(listaGeral, "dataCadastro");

            return listaGeral;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private void validacoes(ClienteObservacaoDTO dto) throws ServiceException {
        if (Uteis.nullOrEmpty(dto.getObservacao())) {
            throw new ServiceException(messageSource.getMessage("cliente.observacao.observacao.nao.informado", null, new Locale(requestService.getLocale())));
        }
        if (dto.getUsuario() == null || Uteis.intNullOrEmpty(dto.getUsuario().getCodigo())) {
            throw new ServiceException(messageSource.getMessage("cliente.observacao.usuario.nao.informado", null, new Locale(requestService.getLocale())));
        }
        if (dto.getCliente() == null || Uteis.intNullOrEmpty(dto.getCliente().getCodigo())) {
            throw new ServiceException(messageSource.getMessage("cliente.observacao.cliente.nao.informado", null, new Locale(requestService.getLocale())));
        }
    }

    public List<ClienteObservacaoDTO> buscarObservacaoTelaCliente(Integer codPessoa, String matricula,
                                                                  JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<ClienteObservacaoDTO> listaGeral = new ArrayList<>();
            List<ClienteObservacaoDTO> listaZW = findAllByCodPessoa(codPessoa, filtros, paginadorDTO);
            listaZW.forEach(dto -> {
                dto.setTipo("v2");
            });
            listaGeral.addAll(listaZW);
            if (!UteisValidacao.emptyString(matricula)) {
                List<ClienteObservacaoDTO> listaTR = obterObservacoesTreino(matricula);
                listaTR.forEach(dto -> {
                    dto.setTipo("v1");
                });
                listaGeral.addAll(listaTR);
            }

            if (paginadorDTO == null || paginadorDTO.getSort() == null) {
                Ordenacao.ordenarListaReverse(listaGeral, "dataCadastro");
            }
            return listaGeral;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    private List<ClienteObservacaoDTO> obterObservacoesTreino(String matricula) throws Exception {
        try {
            ClientDiscoveryDataDTO clientDiscoveryDataDTO = discoveryService.getClientDiscovery();
            String urlTR = clientDiscoveryDataDTO.getServiceUrls().getTreinoApiUrl();
            if (UteisValidacao.emptyString(urlTR)) {
                throw new Exception("TreinoApiUrl não informado");
            }

            StringBuilder apiTR = new StringBuilder(urlTR);
            apiTR.append("/psec/alunos/").append(matricula).append("/observacoes?matricula=true");

            ResponseEntity<String> responseTr = httpServico.doJson(
                    apiTR.toString(),
                    null, HttpMethod.GET, requestService.getToken());

            JSONArray jsonArrayCM = new JSONObject(new JSONObject(responseTr).getString("body")).getJSONArray("content");
            if (jsonArrayCM == null) {
                jsonArrayCM = new JSONArray();
            }
            List<ClienteObservacaoDTO> lista = new ArrayList<>();
            for (int e = 0; e < jsonArrayCM.length(); e++) {
                JSONObject jsonCM = jsonArrayCM.getJSONObject(e);

                ClienteObservacaoDTO dto = new ClienteObservacaoDTO();
                dto.setCodigo(jsonCM.getInt("id"));
                dto.setTipo("v1");
                dto.setObservacao(jsonCM.optString("observacao"));
                dto.setObservacaoSemHTML(dto.getObservacao());
                dto.setImportante(jsonCM.optBoolean("importante"));
                dto.setDataCadastro(new Date(jsonCM.optLong("data")));
                dto.setUsuario(new UsuarioDTO());
                dto.getUsuario().setNome(jsonCM.optString("usuario"));
                lista.add(dto);
            }
            return lista;
        } catch (Exception ex) {
            ex.printStackTrace();
            return new ArrayList<>();
        }
    }

    public List<ClienteObservacaoDTO> findAllByClienteTreino(Integer codClienteTreino) throws ServiceException {
        List<ClienteObservacaoTreino> lista = clienteObservacaoTreinoRepository.findAllByCliente(codClienteTreino);

        List<ClienteObservacaoDTO> dtos = new ArrayList<>();
        for (ClienteObservacaoTreino obs : lista) {
            ClienteObservacaoDTO clienteObservacaoDTO = new ClienteObservacaoDTO();
            clienteObservacaoDTO.setCodigo(obs.getCodigo());
            clienteObservacaoDTO.setObservacao(obs.getObservacao());
            clienteObservacaoDTO.setDataCadastro(obs.getDataObservacao());
            ClienteDTO clienteDTO = new ClienteDTO();
            clienteDTO.setCodigo(obs.getCliente());
            clienteObservacaoDTO.setCliente(clienteDTO);

            UsuarioDTO usuarioDTO = new UsuarioDTO();
            usuarioDTO.setCodigo(obs.getUsuario_codigo());
            clienteObservacaoDTO.setUsuario(usuarioDTO);

            clienteObservacaoDTO.setImportante(obs.getImportante());
            dtos.add(clienteObservacaoDTO);
        }

        return dtos;
    }


}
