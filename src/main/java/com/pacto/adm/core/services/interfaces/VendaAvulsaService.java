package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.PacotePersonalDTO;
import com.pacto.adm.core.dto.PessoaDTO;
import com.pacto.adm.core.dto.VendaAvulsaDTO;
import com.pacto.adm.core.dto.negociacao.ResultadoVendaDTO;
import com.pacto.adm.core.dto.negociacao.ResultadoVendaV2DTO;
import com.pacto.adm.core.entities.Cliente;
import com.pacto.adm.core.entities.Produto;
import com.pacto.adm.core.entities.Usuario;
import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.adm.core.entities.financeiro.VendaAvulsa;
import com.pacto.config.exceptions.ServiceException;

import java.util.Date;
import java.util.List;

public interface VendaAvulsaService {

    VendaAvulsa gerarVendaAtestado(Cliente cliente, Date dataInicio, Date dataFinal, Produto produto, Double valor, <PERSON><PERSON><PERSON> responsavel, Empresa empresa) throws Exception;

    List<PessoaDTO> consultarCompradores(String nome, Integer matricula, Boolean ultimosAcessos) throws ServiceException;

    ResultadoVendaDTO incluirVenda(VendaAvulsaDTO vendaAvulsaDTO) throws ServiceException;

    List<PacotePersonalDTO> consultarPacotes(Integer produto) throws ServiceException;

    PessoaDTO pessoaCodigo(Integer codigo) throws ServiceException;

    ResultadoVendaV2DTO incluirVendav2(VendaAvulsaDTO vendaAvulsaDTO) throws ServiceException;
}
