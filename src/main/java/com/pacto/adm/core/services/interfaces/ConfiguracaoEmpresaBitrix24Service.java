package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.integracoes.ConfiguracaoEmpresaBitrix24DTO;
import com.pacto.config.exceptions.ServiceException;

public interface ConfiguracaoEmpresaBitrix24Service {

    ConfiguracaoEmpresaBitrix24DTO findByEmpresa() throws Exception;

    ConfiguracaoEmpresaBitrix24DTO salvarConfiguracaoEmpresaBitrix24(ConfiguracaoEmpresaBitrix24DTO configDTO) throws ServiceException;

}
