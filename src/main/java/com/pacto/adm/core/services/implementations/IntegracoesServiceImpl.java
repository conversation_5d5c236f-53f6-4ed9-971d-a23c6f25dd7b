package com.pacto.adm.core.services.implementations;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.pacto.adm.core.adapters.ConfiguracaoIntegracaoBuzzLeadAdapter;
import com.pacto.adm.core.adapters.empresa.ConfiguracaoEmpresaRDStationAdapter;
import com.pacto.adm.core.adapters.empresa.EmpresaAdapter;
import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoAmigoFitAdapter;
import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoCDLSPCAdapter;
import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoConciliadoraAdapter;
import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoDelsoftAdapter;
import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoEstacionamentoAdapter;
import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoF360RelatorioAdapter;
import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoGoGoodAdapter;
import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoGympassAdapter;
import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoKobanaAdapter;
import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoManyChatAdapter;
import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoMentorWebAdapter;
import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoMyWellnessAdapter;
import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoNotificaoWebhookAdapter;
import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoNuvemshopAdapter;
import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoParceiroFidelidadeAdapter;
import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoPjbankAdapter;
import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoPluggyAdapter;
import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoRecursoFacilitePayAdapter;
import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoSescDfAdapter;
import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoSmsAdapter;
import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoSpiviAdapter;
import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoTotalpassAdapter;
import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoVitioAdapter;
import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoWeHelpAdapter;
import com.pacto.adm.core.adapters.integracoes.EmpresaConfigEstacionamentoAdapter;
import com.pacto.adm.core.dao.interfaces.ConfigTotalPassDao;
import com.pacto.adm.core.dao.interfaces.EmpresaDao;
import com.pacto.adm.core.dao.interfaces.IntegracaoKobanaDao;
import com.pacto.adm.core.dao.interfaces.ParceiroFidelidadeDao;
import com.pacto.adm.core.dao.interfaces.PluggyItemDao;
import com.pacto.adm.core.dto.base.ClientDiscoveryDataDTO;
import com.pacto.adm.core.dto.filtros.FiltroEmpresaJSON;
import com.pacto.adm.core.dto.filtros.FiltroIntegracoesJSON;
import com.pacto.adm.core.dto.filtros.FiltroProcessarConciliadoraJSON;
import com.pacto.adm.core.dto.integracaomanychat.TagDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoAmigoFitDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoCDLSPCDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoConciliadoraDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoDelsoftDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoEstacionamentoDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoF360RelatorioDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoGoGoodDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoGymPassDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoManyChatDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoMentorWebDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoMyWellnessDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoNotificacaoWebhookDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoNuvemshopDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoParceiroFidelidadeDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoPluggyDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoRecursosFacilitePayDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoSescDfDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoSistemaContabilAlterDataDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoSmsDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoSpiviDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoTotalPassDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoVitioDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoWeHelpDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracoesIntegracoesEmpresasDTO;
import com.pacto.adm.core.entities.ConfigTotalPass;
import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.adm.core.entities.empresa.EmpresaConfigEstacionamento;
import com.pacto.adm.core.entities.kobana.IntegracaoKobana;
import com.pacto.adm.core.entities.pluggyItem.PluggyItem;
import com.pacto.adm.core.enumerador.TipoVigenciaMyWellnessGymPassEnum;
import com.pacto.adm.core.services.interfaces.ConfiguracaoEmpresaBitrix24Service;
import com.pacto.adm.core.services.interfaces.ConfiguracaoEmpresaHubSpotService;
import com.pacto.adm.core.services.interfaces.ConfiguracaoEmpresaRDStationService;
import com.pacto.adm.core.services.interfaces.ConfiguracaoIntegracaoBotConversa;
import com.pacto.adm.core.services.interfaces.ConfiguracaoIntegracaoBuzzLeadService;
import com.pacto.adm.core.services.interfaces.ConfiguracaoIntegracaoFogueteService;
import com.pacto.adm.core.services.interfaces.ConfiguracaoIntegracaoGenericaLeadsService;
import com.pacto.adm.core.services.interfaces.ConfiguracaoIntegracaoGymbotProService;
import com.pacto.adm.core.services.interfaces.ConfiguracaoIntegracaoJoinService;
import com.pacto.adm.core.services.interfaces.ConfiguracaoIntegracaoWordPressService;
import com.pacto.adm.core.services.interfaces.ConfiguracaoIntegracaoGenericaLeadsGymbotService;
import com.pacto.adm.core.services.interfaces.ConfiguracaoIntegracaoEnvioAcessoPratiqueService;
import com.pacto.adm.core.services.interfaces.DiscoveryService;
import com.pacto.adm.core.services.interfaces.IntegracoesService;
import com.pacto.adm.core.services.interfaces.ParceiroFidelidadeService;
import com.pacto.adm.core.util.Util;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.config.utils.HttpServico;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.hibernate.engine.spi.SessionImplementor;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;


@Service
public class IntegracoesServiceImpl implements IntegracoesService {

    @Autowired
    private DiscoveryService discoveryService;

    private final MessageSource messageSource;
    private final RequestService requestService;
    private final HttpServico httpServico;

    private final ConfiguracaoIntegracaoMyWellnessAdapter myWellnessAdapter;
    private final ConfiguracaoIntegracaoMentorWebAdapter mentorWebAdapter;
    private final ConfiguracaoIntegracaoEstacionamentoAdapter estacionamentoAdapter;
    private final ConfiguracaoIntegracaoCDLSPCAdapter cdlspcAdapter;
    private final ConfiguracaoIntegracaoRecursoFacilitePayAdapter recursoFacilitePayAdapter;
    private final ConfiguracaoIntegracaoDelsoftAdapter delsoftAdapter;
    private final ConfiguracaoIntegracaoVitioAdapter vitioAdapter;
    private final EmpresaConfigEstacionamentoAdapter empresaConfigEstacionamentoAdapter;
    private final ConfiguracaoIntegracaoParceiroFidelidadeAdapter configParceiroFidelidadeAdapter;
    private final ParceiroFidelidadeDao parceiroFidelidadeDao;
    private final ConfigTotalPassDao configTotalPassDao;
    private final ParceiroFidelidadeService parceiroFidelidadeService;
    private final ConfiguracaoIntegracaoGympassAdapter gympassAdapter;
    private final ConfiguracaoIntegracaoGoGoodAdapter goGoodAdapter;
    private final ConfiguracaoIntegracaoTotalpassAdapter totalpassAdapter;
    private final ConfiguracaoIntegracaoSpiviAdapter spiviAdapter;
    private final ConfiguracaoIntegracaoNotificaoWebhookAdapter notificaoWebhookAdapter;
    private final ConfiguracaoIntegracaoAmigoFitAdapter amigoFitAdapter;
    private final ConfiguracaoIntegracaoWeHelpAdapter weHelpAdapter;
    private final ConfiguracaoIntegracaoBuzzLeadService confgBuzzLeadService;
    private final ConfiguracaoIntegracaoSmsAdapter smsAdapter;
    private final ConfiguracaoEmpresaRDStationService configuracaoEmpresaRDStationService;
    private final ConfiguracaoEmpresaHubSpotService configuracaoEmpresaHubSpotService;

    private final ConfiguracaoEmpresaBitrix24Service configuracaoEmpresaBitrix24Service;
    private final ConfiguracaoIntegracaoBotConversa configuracaoIntegracaoBotConversaService;
    private final ConfiguracaoIntegracaoGymbotProService configuracaoIntegracaoGymbotProService;
    private final ConfiguracaoIntegracaoWordPressService configuracaoIntegracaoWordPressService;
    private final ConfiguracaoIntegracaoJoinService configuracaoIntegracaoJoinService;
    private final ConfiguracaoIntegracaoGenericaLeadsService configuracaoIntegracaoGenericaLeadsService;
    private final ConfiguracaoIntegracaoGenericaLeadsGymbotService configuracaoIntegracaoGenericaLeadsGymbotService;
    private final ConfiguracaoIntegracaoF360RelatorioAdapter f360RelatorioAdapter;
    private final ConfiguracaoIntegracaoConciliadoraAdapter conciliadoraAdapter;
    private final ConfiguracaoIntegracaoPjbankAdapter pjbankAdapter;
    private final ConfiguracaoIntegracaoPluggyAdapter pluggyAdapter;
    private final ConfiguracaoIntegracaoNuvemshopAdapter nuvemshopAdapter;
    private final ConfiguracaoIntegracaoKobanaAdapter integracaoKobanaAdapter;
    private final ConfiguracaoIntegracaoFogueteService fogueteService;
    private final ConfiguracaoIntegracaoManyChatAdapter manyChatAdapter;
    private final ConfiguracaoIntegracaoEnvioAcessoPratiqueService configuracaoIntegracaoEnvioAcessoPratiqueService;
    private final ConfiguracaoIntegracaoSescDfAdapter configuracaoIntegracaoSescDfAdapter;

    private final EmpresaDao empresaDao;
    private final PluggyItemDao pluggyItemDao;
    private final IntegracaoKobanaDao integracaoKobanaDao;
    private final EmpresaServiceImpl empresaService;
    private final EmpresaAdapter empresaAdapter;

    public IntegracoesServiceImpl(MessageSource messageSource, RequestService requestService, HttpServico httpServico, ConfiguracaoIntegracaoMyWellnessAdapter myWellnessAdapter,
                                  ConfiguracaoIntegracaoMentorWebAdapter mentorWebAdapter, ConfiguracaoIntegracaoEstacionamentoAdapter estacionamentoAdapter,
                                  ConfiguracaoIntegracaoCDLSPCAdapter cdlspcAdapter,
                                  ConfiguracaoIntegracaoRecursoFacilitePayAdapter recursoFacilitePayAdapter,
                                  EmpresaConfigEstacionamentoAdapter empresaConfigEstacionamentoAdapter,
                                  ConfiguracaoIntegracaoParceiroFidelidadeAdapter configParceiroFidelidadeAdapter,
                                  ParceiroFidelidadeDao parceiroFidelidadeDao, ConfigTotalPassDao configTotalPassDao,
                                  ParceiroFidelidadeService parceiroFidelidadeService, ConfiguracaoIntegracaoGympassAdapter gympassAdapter, ConfiguracaoIntegracaoGoGoodAdapter goGoodAdapter,
                                  ConfiguracaoIntegracaoTotalpassAdapter totalpassAdapter, ConfiguracaoIntegracaoSpiviAdapter spiviAdapter, ConfiguracaoIntegracaoNotificaoWebhookAdapter notificaoWebhookAdapter,
                                  ConfiguracaoIntegracaoAmigoFitAdapter amigoFitAdapter, ConfiguracaoIntegracaoWeHelpAdapter weHelpAdapter, ConfiguracaoIntegracaoBuzzLeadAdapter buzzLeadAdapter, ConfiguracaoIntegracaoBuzzLeadService confgBuzzLeadService, ConfiguracaoEmpresaRDStationAdapter rdStationAdapter,
                                  ConfiguracaoIntegracaoDelsoftAdapter delsoftAdapter, ConfiguracaoIntegracaoVitioAdapter vitioAdapter, ConfiguracaoIntegracaoSmsAdapter smsAdapter,
                                  ConfiguracaoEmpresaRDStationService configuracaoEmpresaRDStationService, ConfiguracaoEmpresaHubSpotService configuracaoEmpresaHubSpotService,
                                  ConfiguracaoEmpresaBitrix24Service configuracaoEmpresaBitrix24Service, ConfiguracaoIntegracaoWordPressService configuracaoIntegracaoWordPressService, ConfiguracaoIntegracaoJoinService configuracaoIntegracaoJoinService,
                                  ConfiguracaoIntegracaoGenericaLeadsService configuracaoIntegracaoGenericaLeadsService, ConfiguracaoIntegracaoBotConversa configuracaoIntegracaoBotConversaService,
                                  ConfiguracaoIntegracaoGenericaLeadsGymbotService configuracaoIntegracaoGenericaLeadsGymbotService,
                                  ConfiguracaoIntegracaoF360RelatorioAdapter f360RelatorioAdapter, ConfiguracaoIntegracaoConciliadoraAdapter conciliadoraAdapter, ConfiguracaoIntegracaoPjbankAdapter pjbankAdapter, ConfiguracaoIntegracaoKobanaAdapter integracaoKobanaAdapter,
                                  ConfiguracaoIntegracaoNuvemshopAdapter nuvemshopAdapter, EmpresaDao empresaDao, EmpresaServiceImpl empresaService, EmpresaAdapter empresaAdapter, ConfiguracaoIntegracaoPluggyAdapter pluggyAdapter, PluggyItemDao pluggyItemDao,
                                  ConfiguracaoIntegracaoGymbotProService configuracaoIntegracaoGymbotProService, IntegracaoKobanaDao integracaoKobanaDao, ConfiguracaoIntegracaoFogueteService fogueteService, ConfiguracaoIntegracaoManyChatAdapter manyChatAdapter, ConfiguracaoIntegracaoEnvioAcessoPratiqueService configuracaoIntegracaoEnvioAcessoPratiqueService,
                                  ConfiguracaoIntegracaoSescDfAdapter configuracaoIntegracaoSescDfAdapter) {
        this.messageSource = messageSource;
        this.requestService = requestService;
        this.httpServico = httpServico;
        this.myWellnessAdapter = myWellnessAdapter;
        this.mentorWebAdapter = mentorWebAdapter;
        this.estacionamentoAdapter = estacionamentoAdapter;
        this.cdlspcAdapter = cdlspcAdapter;
        this.recursoFacilitePayAdapter = recursoFacilitePayAdapter;
        this.empresaConfigEstacionamentoAdapter = empresaConfigEstacionamentoAdapter;
        this.configParceiroFidelidadeAdapter = configParceiroFidelidadeAdapter;
        this.parceiroFidelidadeDao = parceiroFidelidadeDao;
        this.configTotalPassDao = configTotalPassDao;
        this.parceiroFidelidadeService = parceiroFidelidadeService;
        this.gympassAdapter = gympassAdapter;
        this.goGoodAdapter = goGoodAdapter;
        this.totalpassAdapter = totalpassAdapter;
        this.spiviAdapter = spiviAdapter;
        this.notificaoWebhookAdapter = notificaoWebhookAdapter;
        this.amigoFitAdapter = amigoFitAdapter;
        this.weHelpAdapter = weHelpAdapter;
        this.confgBuzzLeadService = confgBuzzLeadService;
        this.delsoftAdapter = delsoftAdapter;
        this.vitioAdapter = vitioAdapter;
        this.smsAdapter = smsAdapter;
        this.configuracaoEmpresaRDStationService = configuracaoEmpresaRDStationService;
        this.configuracaoEmpresaHubSpotService = configuracaoEmpresaHubSpotService;
        this.configuracaoEmpresaBitrix24Service = configuracaoEmpresaBitrix24Service;
        this.configuracaoIntegracaoWordPressService = configuracaoIntegracaoWordPressService;
        this.configuracaoIntegracaoJoinService = configuracaoIntegracaoJoinService;
        this.configuracaoIntegracaoGenericaLeadsService = configuracaoIntegracaoGenericaLeadsService;
        this.configuracaoIntegracaoGenericaLeadsGymbotService = configuracaoIntegracaoGenericaLeadsGymbotService;
        this.configuracaoIntegracaoBotConversaService = configuracaoIntegracaoBotConversaService;
        this.f360RelatorioAdapter = f360RelatorioAdapter;
        this.conciliadoraAdapter = conciliadoraAdapter;
        this.pjbankAdapter = pjbankAdapter;
        this.integracaoKobanaAdapter = integracaoKobanaAdapter;
        this.nuvemshopAdapter = nuvemshopAdapter;
        this.pluggyAdapter = pluggyAdapter;
        this.empresaDao = empresaDao;
        this.pluggyItemDao = pluggyItemDao;
        this.empresaService = empresaService;
        this.empresaAdapter = empresaAdapter;
        this.integracaoKobanaDao = integracaoKobanaDao;
        this.configuracaoIntegracaoGymbotProService = configuracaoIntegracaoGymbotProService;
        this.fogueteService = fogueteService;
        this.manyChatAdapter = manyChatAdapter;
        this.configuracaoIntegracaoEnvioAcessoPratiqueService = configuracaoIntegracaoEnvioAcessoPratiqueService;
        this.configuracaoIntegracaoSescDfAdapter = configuracaoIntegracaoSescDfAdapter;
    }

    @Override
    public ConfiguracoesIntegracoesEmpresasDTO findAllByFilters(FiltroIntegracoesJSON filtros) throws Exception {
        validarFiltros(filtros);

        empresaDao.getCurrentSession().clear();
        final Empresa empresa = empresaDao.findById(filtros.getCodigoEmpresa());

        final ConfiguracoesIntegracoesEmpresasDTO configuracoesIntegracoesDTO = new ConfiguracoesIntegracoesEmpresasDTO();

        switch (filtros.getModuloEnum()) {
            case ADM: {
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoMyWellness(myWellnessAdapter.toDto(empresa));
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoMentorWeb(mentorWebAdapter.toDto(empresa));
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoEstacionamento(estacionamentoAdapter.toDto(empresa));
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoDelsoft(delsoftAdapter.toDto(empresa));
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoVitio(vitioAdapter.toDto(empresa));
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoParceiroFidelidade(configParceiroFidelidadeAdapter.toDto(empresa, parceiroFidelidadeDao.findByEmpresaId(empresa.getCodigo())));
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoNuvemshop(nuvemshopAdapter.toDto(empresa));
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoFoguete(fogueteService.findByEmpresaId(empresa.getCodigo()));
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoEnvioAcessoPratique(configuracaoIntegracaoEnvioAcessoPratiqueService.findByEmpresaId(empresa.getCodigo()));
                break;
            }
            case CRM: {
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoNotificacaoWebhook(notificaoWebhookAdapter.toDto(empresa));
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoAmigoFit(amigoFitAdapter.toDto(empresa));
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoWeHelp(weHelpAdapter.toDto(empresa, requestService.getUsuarioAtual().getChave()));
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoRDStation(configuracaoEmpresaRDStationService.findByEmpresaId(empresa.getCodigo()));
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoBuzzLead(confgBuzzLeadService.findByEmpresaId(empresa.getCodigo()));
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoSms(smsAdapter.toDto(empresa));
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoWordPress(configuracaoIntegracaoWordPressService.findByEmpresaId(empresa.getCodigo()));
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoJoin(configuracaoIntegracaoJoinService.findByEmpresaId(empresa.getCodigo()));
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoGenericaLeads(configuracaoIntegracaoGenericaLeadsService.findByEmpresaId(empresa.getCodigo()));
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoGenericaLeadsGymbot(configuracaoIntegracaoGenericaLeadsGymbotService.findByEmpresaId(empresa.getCodigo()));
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoHubSpot(configuracaoEmpresaHubSpotService.findByEmpresaId(empresa.getCodigo()));
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoBotConversa(configuracaoIntegracaoBotConversaService.findByEmpresaId(empresa.getCodigo()));
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoGymbotPro(configuracaoIntegracaoGymbotProService.findByEmpresaId(empresa.getCodigo()));
                configuracoesIntegracoesDTO.setConfiguracaoEmpresaBitrix24(configuracaoEmpresaBitrix24Service.findByEmpresa());
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoManyChat(manyChatAdapter.toDto(empresa));
                break;
            }
            case FINANCEIRO: {
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoF360Relatorio(f360RelatorioAdapter.toDto(empresa));
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoConciliadora(conciliadoraAdapter.toDto(empresa));
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoSistemaContabilAlterData(findConfiguracaoIntegracaoSistemaContabilAlterData());
                break;
            }
            case TREINO: {
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoSpivi(spiviAdapter.toDto(empresa));
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoTotalpass(totalpassAdapter.toDto(empresa, configTotalPassDao.findByEmpresaId(empresa.getCodigo())));
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoGympass(gympassAdapter.toDto(empresa));
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoGoGood(goGoodAdapter.toDto(empresa));
                break;
            }
            case PACTO_PAY: {
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoPjBank(pjbankAdapter.toDto(empresa));
                break;
            }
            case FACILITE_PAY: {
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoPluggy(consultarConfiguracoesIntegracoesPluggy(empresa.getCodigo()));
                try {
                    configuracoesIntegracoesDTO.setConfiguracaoIntegracaoKobana(integracaoKobanaAdapter.toDto(consultarConfiguracaoIntegracaoKobana(empresa.getCodigo()), empresa));
                } catch (Exception ignore) {

                }
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoCDLSPC(cdlspcAdapter.toDto(empresa));
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoRecursosFacilitePay(recursoFacilitePayAdapter.toDto(empresa));
                break;
            }
            case SISTEMA_SESC: {
                configuracoesIntegracoesDTO.setConfiguracaoIntegracaoSescDf(configuracaoIntegracaoSescDfAdapter.toDto(empresa));
                break;
            }
        }
        return configuracoesIntegracoesDTO;
    }

    private void validarFiltros(FiltroIntegracoesJSON filtros) throws ServiceException {
        if (UteisValidacao.emptyNumber(filtros.getCodigoEmpresa())) {
            throw new ServiceException(messageSource.getMessage("integracoes.codigoempresa.nao.informado", null, new Locale(requestService.getLocale())));
        }
        if (filtros.getModuloEnum() == null) {
            throw new ServiceException(messageSource.getMessage("integracoes.modulo.nao.informado", null, new Locale(requestService.getLocale())));
        }
    }

    @Override
    public List<ConfiguracaoIntegracaoPluggyDTO> consultarConfiguracoesIntegracoesPluggy(int empresa) throws Exception {
        List<ConfiguracaoIntegracaoPluggyDTO> configuracaoIntegracaoPluggyDTO = new ArrayList<>();

        this.pluggyItemDao.getCurrentSession().clear();

        List<PluggyItem> pluggyItem = this.pluggyItemDao.findByEmpresaAndAtivo(empresa);
        if (!UteisValidacao.emptyList(pluggyItem)) {
            configuracaoIntegracaoPluggyDTO.addAll(pluggyAdapter.toDtos(pluggyItem));
        }

        return configuracaoIntegracaoPluggyDTO;
    }

    @Override
    public IntegracaoKobana consultarConfiguracaoIntegracaoKobana(int empresa) throws Exception {
        IntegracaoKobana integracaoKobana = new IntegracaoKobana();

        this.integracaoKobanaDao.getCurrentSession().clear();

        integracaoKobana = this.integracaoKobanaDao.findByEmpresa(empresa);

        return integracaoKobana;
    }

    public ConfiguracaoIntegracaoGymPassDTO consultarConfiguracoesIntegracoesGymPass(Integer empresa) throws Exception {
        this.empresaDao.getCurrentSession().clear();
        Empresa empresaObj = empresaDao.findById(empresa);
        return gympassAdapter.toDto(empresaObj);
    }

    public ConfiguracaoIntegracaoGoGoodDTO consultarConfiguracoesIntegracoesGoGood(Integer empresa) throws Exception {
        this.empresaDao.getCurrentSession().clear();
        Empresa empresaObj = empresaDao.findById(empresa);
        return goGoodAdapter.toDto(empresaObj);
    }

    @Override
    public List<ConfiguracaoIntegracaoGymPassDTO> consultarConfiguracoesIntegracoesGymPass() throws Exception {
        List<ConfiguracaoIntegracaoGymPassDTO> configuracoesIntegracaoGympass = new ArrayList<>();

        this.empresaDao.getCurrentSession().clear();
        FiltroEmpresaJSON filtroEmpresa = new FiltroEmpresaJSON("", 0, true);
        filtroEmpresa.setAtiva(true);
        List<Empresa> empresas = empresaDao.findAll(filtroEmpresa);

        empresas.forEach(e -> {
            configuracoesIntegracaoGympass.add(gympassAdapter.toDto(e));
        });

        return configuracoesIntegracaoGympass;
    }

    @Override
    public List<ConfiguracaoIntegracaoGoGoodDTO> consultarConfiguracoesIntegracoesGogood() throws Exception {
        List<ConfiguracaoIntegracaoGoGoodDTO> configuracoesIntegracaoGympass = new ArrayList<>();

        this.empresaDao.getCurrentSession().clear();
        FiltroEmpresaJSON filtroEmpresa = new FiltroEmpresaJSON("", 0, true);
        filtroEmpresa.setAtiva(true);
        List<Empresa> empresas = empresaDao.findAll(filtroEmpresa);

        empresas.forEach(e -> {
            configuracoesIntegracaoGympass.add(goGoodAdapter.toDto(e));
        });

        return configuracoesIntegracaoGympass;
    }

    @Override
    public List<ConfiguracaoIntegracaoTotalPassDTO> consultarConfiguracoesIntegracoesTotalPass() throws Exception {
        List<ConfiguracaoIntegracaoTotalPassDTO> configuracoesIntegracaoTotalpass = new ArrayList<>();

        this.empresaDao.getCurrentSession().clear();
        FiltroEmpresaJSON filtroEmpresa = new FiltroEmpresaJSON("", 0, true);
        filtroEmpresa.setAtiva(true);
        List<Empresa> empresas = empresaDao.findAll(filtroEmpresa);

        empresas.forEach(e -> {
            try {
                configuracoesIntegracaoTotalpass.add(totalpassAdapter.toDto(e, configTotalPassDao.findByEmpresaId(e.getCodigo())));
            } catch (Exception ex) {
                throw new RuntimeException(ex);
            }
        });

        return configuracoesIntegracaoTotalpass;
    }

    public ConfiguracaoIntegracaoTotalPassDTO consultarConfiguracoesIntegracoesTotalPass(Integer empresa) throws Exception {
        this.empresaDao.getCurrentSession().clear();
        Empresa objEmpresa = empresaDao.findById(empresa);
        return totalpassAdapter.toDto(objEmpresa, configTotalPassDao.findByEmpresaId(objEmpresa.getCodigo()));
    }

    @Override
    public List<ConfiguracaoIntegracaoSpiviDTO> consultarConfiguracoesIntegracoesSpivi() throws Exception {
        List<ConfiguracaoIntegracaoSpiviDTO> configuracaoIntegracaoSpiviDTOS = new ArrayList<>();

        this.empresaDao.getCurrentSession().clear();
        FiltroEmpresaJSON filtroEmpresa = new FiltroEmpresaJSON("", 0, true);
        filtroEmpresa.setAtiva(true);
        List<Empresa> empresas = empresaDao.findAll(filtroEmpresa);

        empresas.forEach(empresa -> {
            configuracaoIntegracaoSpiviDTOS.add(spiviAdapter.toDto(empresa));
        });

        return configuracaoIntegracaoSpiviDTOS;
    }

    public void salvarConfiguracaoIntegracaoGympass(ConfiguracaoIntegracaoGymPassDTO integracaoGymPassDTO) throws ServiceException {
        try {
            this.empresaDao.getCurrentSession().clear();
            Empresa empresa = empresaDao.findById(integracaoGymPassDTO.getEmpresa().getCodigo());
            empresa.setCodigoGymPass(integracaoGymPassDTO.getCodigoGympass());
            empresa.setTokenApiGymPass(integracaoGymPassDTO.getTokenApiGympass());
            empresa.setLimiteDeAcessosPorDiaGympass(integracaoGymPassDTO.getLimiteDeAcessosPorDia());
            empresa.setLimiteDeAulasPorDiaGympass(integracaoGymPassDTO.getLimiteDeAulasPorDia());
            empresaService.update(empresa);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    public void salvarConfiguracaoIntegracaoGoGood(ConfiguracaoIntegracaoGoGoodDTO integracaoGoGoodDTO) throws ServiceException {
        try {
            this.empresaDao.getCurrentSession().clear();
            Empresa empresa = empresaDao.findById(integracaoGoGoodDTO.getEmpresa().getCodigo());
            empresa.setTokenAcademyGogood(integracaoGoGoodDTO.getTokenAcademyGoGood());
            empresaService.update(empresa);
            inserirChaveWebhookGogood(empresa);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    private void inserirChaveWebhookGogood(Empresa empresa) throws Exception {
        try {
            ClientDiscoveryDataDTO clientDiscoveryDataDTO = discoveryService.getClientDiscovery();
            String zwUrl = clientDiscoveryDataDTO.getServiceUrls().getZwUrl();
            if (UteisValidacao.emptyString(zwUrl)) {
                throw new Exception("ZwUrl não informado");
            }

            JSONObject jsonBody = new JSONObject();
            jsonBody.put("empresa", empresa.getCodigo());
            jsonBody.put("tokenAcademy", empresa.getTokenAcademyGogood());
            jsonBody.put("operacao", "cadastrarChaveWebhook");

            StringBuilder urlZw = new StringBuilder(zwUrl);
            urlZw.append("/prest/tela-cliente")
                    .append("?key=").append(requestService.getUsuarioAtual().getChave())
                    .append("&op=gogood");

            ResponseEntity<String> responseZw = httpServico.doJson(
                    urlZw.toString(),
                    jsonBody.toString(), HttpMethod.POST, "");
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar("Erro ao realizar inserção chave webhook gogood: " + ex.getMessage());
        }
    }

    public void salvarConfiguracaoIntegracaoTotalpass(ConfiguracaoIntegracaoTotalPassDTO integracaoTotalPassDTO) throws ServiceException {
        try {
            this.empresaDao.getCurrentSession().clear();
            ConfigTotalPass register = configTotalPassDao.findByEmpresaId(integracaoTotalPassDTO.getEmpresa().getCodigo());

            if (register == null) {
                register = new ConfigTotalPass();
                register.setEmpresa_codigo(integracaoTotalPassDTO.getEmpresa().getCodigo());
            }

            register.setCodigoTotalPass(integracaoTotalPassDTO.getCodigoTotalpass());
            register.setApikey(integracaoTotalPassDTO.getApiKey());
            register.setLimiteDeAulasPorDia(integracaoTotalPassDTO.getLimiteDeAulasPorDia());
            register.setLimiteDeAcessosPorDia(integracaoTotalPassDTO.getLimiteDeAcessosPorDia());
            register.setPermitirWod(integracaoTotalPassDTO.getPermitirWod());
            register.setInativo(integracaoTotalPassDTO.getInativo());

            if (UteisValidacao.emptyNumber(register.getCodigo())) {
                configTotalPassDao.save(register);
            } else {
                configTotalPassDao.update(register);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    public void salvarConfiguracaoIntegracaoSpivi(ConfiguracaoIntegracaoSpiviDTO spiviDTO) throws ServiceException {
        try {
            if (spiviDTO.isHabilitada()) {
                if (Uteis.nullOrEmpty(spiviDTO.getSourceName())) {
                    throw new ServiceException(messageSource.getMessage("integracao.spivi.sourcename.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (spiviDTO.getSiteId() == null) {
                    throw new ServiceException(messageSource.getMessage("integracao.spivi.siteid.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (Uteis.nullOrEmpty(spiviDTO.getSpiviPassword())) {
                    throw new ServiceException(messageSource.getMessage("integracao.spivi.password.nao.informado", null, new Locale(requestService.getLocale())));
                }
            }
            this.empresaDao.getCurrentSession().clear();
            Empresa empresa = empresaDao.findById(spiviDTO.getEmpresa().getCodigo());
            empresa.setIntegracaoSpiviHabilitada(spiviDTO.isHabilitada());
            empresa.setIntegracaoSpiviSourceName(spiviDTO.getSourceName());
            empresa.setIntegracaoSpiviPassword(spiviDTO.getSpiviPassword());
            empresa.setIntegracaoSpiviSiteID(spiviDTO.getSiteId());
            empresaService.update(empresa);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public void salvarConfiguracaoIntegracaoMyWellness(ConfiguracaoIntegracaoMyWellnessDTO configMyWellnessDTO) throws ServiceException {
        try {
            if (configMyWellnessDTO.isHabilitada()) {
                if (Uteis.nullOrEmpty(configMyWellnessDTO.getFacilityUrl())) {
                    throw new ServiceException(messageSource.getMessage("integracao.mywellness.facility.url.nao.informada", null, new Locale(requestService.getLocale())));
                }
                if (Uteis.nullOrEmpty(configMyWellnessDTO.getApiKey())) {
                    throw new ServiceException(messageSource.getMessage("integracao.mywellness.apikey.nao.informada", null, new Locale(requestService.getLocale())));
                }
                if (configMyWellnessDTO.getTipoVigenciaMyWellnessGympass() == TipoVigenciaMyWellnessGymPassEnum.QUANTIDADE_INFORMADA.getId()) {
                    if (configMyWellnessDTO.getNrDiasVigenciaMyWellnessGymPass() == null || configMyWellnessDTO.getNrDiasVigenciaMyWellnessGymPass() <= 0) {
                        throw new ServiceException(messageSource.getMessage("integracao.mywellness.numero.dias.vigencia.nao.informada", null, new Locale(requestService.getLocale())));
                    }
                }
            }
            configMyWellnessDTO.setUser("<EMAIL>");
            configMyWellnessDTO.setPassword("Pac03092020@");

            empresaDao.getCurrentSession().clear();
            Empresa empresa = myWellnessAdapter.toEntity(configMyWellnessDTO, empresaDao.findById(configMyWellnessDTO.getEmpresa().getCodigo()));
            if (empresa != null) {
                this.empresaService.update(empresa);
            } else {
                throw new ServiceException(messageSource.getMessage("integracao.falha.salvar.integracao", null, new Locale(requestService.getLocale())));
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public void salvarConfiguracaoIntegracaoMentorWeb(ConfiguracaoIntegracaoMentorWebDTO configMentorWebDTO) throws ServiceException {
        try {
            if (configMentorWebDTO.isHabilitada()) {
                if (Uteis.nullOrEmpty(configMentorWebDTO.getUrl())) {
                    throw new ServiceException(messageSource.getMessage("integracao.mentor.web.url.nao.informada", null, new Locale(requestService.getLocale())));
                }
                if (Uteis.nullOrEmpty(configMentorWebDTO.getServico())) {
                    throw new ServiceException(messageSource.getMessage("integracao.mentor.web.nome.servico.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (Uteis.nullOrEmpty(configMentorWebDTO.getUser())) {
                    throw new ServiceException(messageSource.getMessage("integracao.mentor.web.usuario.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (Uteis.nullOrEmpty(configMentorWebDTO.getPassword())) {
                    throw new ServiceException(messageSource.getMessage("integracao.mentor.web.senha.nao.informada", null, new Locale(requestService.getLocale())));
                }
            }

            empresaDao.getCurrentSession().clear();
            Empresa empresa = mentorWebAdapter.toEntity(configMentorWebDTO, empresaDao.findById(configMentorWebDTO.getEmpresa().getCodigo()));
            if (empresa != null) {
                this.empresaService.update(empresa);
            } else {
                throw new ServiceException(messageSource.getMessage("integracao.falha.salvar.integracao", null, new Locale(requestService.getLocale())));
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public void salvarConfiguracaoIntegracaoEstacionamento(ConfiguracaoIntegracaoEstacionamentoDTO configEstacionamentoDTO) throws ServiceException {
        try {
            if (configEstacionamentoDTO.isUtilizaSistemaEstacionamento()) {
                if (Uteis.nullOrEmpty(configEstacionamentoDTO.getEmpresaConfigEstacionamento().getFtpHost())) {
                    throw new ServiceException(messageSource.getMessage("integracao.estacionamento.ftp.host.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (Uteis.intNullOrEmpty(configEstacionamentoDTO.getEmpresaConfigEstacionamento().getFtpPort())) {
                    throw new ServiceException(messageSource.getMessage("integracao.estacionamento.ftp.porta.nao.informada", null, new Locale(requestService.getLocale())));
                }
                if (Uteis.nullOrEmpty(configEstacionamentoDTO.getEmpresaConfigEstacionamento().getFtpUser())) {
                    throw new ServiceException(messageSource.getMessage("integracao.estacionamento.ftp.user.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (Uteis.nullOrEmpty(configEstacionamentoDTO.getEmpresaConfigEstacionamento().getFtpPass())) {
                    throw new ServiceException(messageSource.getMessage("integracao.estacionamento.ftp.pass.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (Uteis.nullOrEmpty(configEstacionamentoDTO.getEmpresaConfigEstacionamento().getNomeArquivo())) {
                    throw new ServiceException(messageSource.getMessage("integracao.estacionamento.nome.arquivo.nao.informado", null, new Locale(requestService.getLocale())));
                }
            }

            EmpresaConfigEstacionamento empresaConfigEstacionamento = empresaConfigEstacionamentoAdapter.toEntity(configEstacionamentoDTO.getEmpresaConfigEstacionamento());
            empresaDao.getCurrentSession().clear();
            Empresa empresa = empresaDao.findById(configEstacionamentoDTO.getEmpresa().getCodigo());
            if (empresa != null) {
                empresa.setUtilizaSistemaEstacionamento(configEstacionamentoDTO.isUtilizaSistemaEstacionamento());
                empresa.setEmpresaConfigEstacionamento(empresaConfigEstacionamento);
                this.empresaService.update(empresa);
            } else {
                throw new ServiceException(messageSource.getMessage("integracao.falha.salvar.integracao", null, new Locale(requestService.getLocale())));
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public void testarConfiguracaoIntegracaoEstacionamento(ConfiguracaoIntegracaoEstacionamentoDTO configEstacionamentoDTO) throws ServiceException {
        try {
            StringBuilder urlZw = new StringBuilder(discoveryService.getClientDiscovery().getServiceUrls().getZwUrl());
            urlZw.append("/prest/integracao/integracao-empresa")
                    .append("?key=").append(requestService.getUsuarioAtual().getChave())
                    .append("&op=estacionamento");

            ObjectMapper objectMapper = new ObjectMapper();

            ResponseEntity<String> responseZw = httpServico.doJson(
                    urlZw.toString(),
                    objectMapper.writeValueAsString(configEstacionamentoDTO.getEmpresaConfigEstacionamento()), HttpMethod.POST, "");

            JSONObject jsonObject = new JSONObject(responseZw);
            if (!jsonObject.optString("erro").isEmpty()) {
                String msg = "Falha ao testar estacionamento! " + jsonObject.optString("erro");
                System.out.println(msg);
                throw new ServiceException(msg);
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public void salvarConfiguracaoIntegracaoCDLSPC(ConfiguracaoIntegracaoCDLSPCDTO configDTO) throws ServiceException {
        try {
            if (configDTO.isConsultarNovoCadastroSPC()) {
                if (configDTO.getEmpresa() == null || UteisValidacao.emptyNumber(configDTO.getEmpresa().getCodigo())) {
                    throw new ServiceException(messageSource.getMessage("integracao.empresa.nao.informada", null, new Locale(requestService.getLocale())));
                }
                if (UteisValidacao.emptyNumber(configDTO.getCodigoAssociadoSPC())) {
                    throw new ServiceException(messageSource.getMessage("integracao.cdlspc.codigoassociado.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (UteisValidacao.emptyString(configDTO.getOperadorSPC())) {
                    throw new ServiceException(messageSource.getMessage("integracao.cdlspc.operador.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (UteisValidacao.emptyString(configDTO.getSenhaSPC())) {
                    throw new ServiceException(messageSource.getMessage("integracao.cdlspc.senha.nao.informada", null, new Locale(requestService.getLocale())));
                }
            }

            empresaDao.getCurrentSession().clear();
            Empresa empresa = cdlspcAdapter.toEntity(configDTO, empresaDao.findById(configDTO.getEmpresa().getCodigo()));
            this.empresaService.update(empresa);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public void salvarConfiguracaoIntegracaoDelsoft(ConfiguracaoIntegracaoDelsoftDTO configDTO) throws ServiceException {
        try {
            if (configDTO.getUtilizaIntegracaoDelsoft()) {
                if (configDTO.getEmpresa() == null || UteisValidacao.emptyNumber(configDTO.getEmpresa().getCodigo())) {
                    throw new ServiceException(messageSource.getMessage("integracao.empresa.nao.informada", null, new Locale(requestService.getLocale())));
                }
                if (UteisValidacao.emptyString(configDTO.getUsuarioAplicacaoDelsoft())) {
                    throw new ServiceException(messageSource.getMessage("integracao.delsoft.usuario.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (UteisValidacao.emptyString(configDTO.getSenhaAplicacaoDelsoft())) {
                    throw new ServiceException(messageSource.getMessage("integracao.delsoft.senha.nao.informada", null, new Locale(requestService.getLocale())));
                }
                if (UteisValidacao.emptyNumber(configDTO.getPlanoAplicacaoDelsoft())) {
                    throw new ServiceException(messageSource.getMessage("integracao.delsoft.plano.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (UteisValidacao.emptyString(configDTO.getHostIntegracaoDelsoft())) {
                    throw new ServiceException(messageSource.getMessage("integracao.delsoft.host.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (UteisValidacao.emptyNumber(configDTO.getPortaIntegracaoDelsoft())) {
                    throw new ServiceException(messageSource.getMessage("integracao.delsoft.porta.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (UteisValidacao.emptyString(configDTO.getNomeAplicacaoDelsoft())) {
                    throw new ServiceException(messageSource.getMessage("integracao.delsoft.nomeaplicacao.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (UteisValidacao.emptyString(configDTO.getTokenIntegracaoDelsoft())) {
                    throw new ServiceException(messageSource.getMessage("integracao.delsoft.token.nao.informado", null, new Locale(requestService.getLocale())));
                }

            }
            empresaDao.getCurrentSession().clear();
            Empresa empresa = delsoftAdapter.toEntity(configDTO, empresaDao.findById(configDTO.getEmpresa().getCodigo()));
            this.empresaService.update(empresa);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    public void salvarConfiguracaoIntegracaoVitio(ConfiguracaoIntegracaoVitioDTO configDTO) throws ServiceException {
        try {
            if (configDTO.isUsaVitio()) {
                if (configDTO.getEmpresa() == null || UteisValidacao.emptyNumber(configDTO.getEmpresa().getCodigo())) {
                    throw new ServiceException(messageSource.getMessage("integracao.empresa.nao.informada", null, new Locale(requestService.getLocale())));
                }
                if (UteisValidacao.emptyString(configDTO.getLinkCheckoutVitio())) {
                    throw new ServiceException(messageSource.getMessage("integracao.vitio.linkcheckout.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (UteisValidacao.emptyString(configDTO.getLinkEbook())) {
                    throw new ServiceException(messageSource.getMessage("integracao.vitio.linkebook.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (UteisValidacao.emptyString(configDTO.getMensagemVitioWpp())) {
                    throw new ServiceException(messageSource.getMessage("integracao.vitio.mensagemwhatsapp.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (UteisValidacao.emptyString(configDTO.getMensagemVitioQuerComprar())) {
                    throw new ServiceException(messageSource.getMessage("integracao.vitio.mensagemquercomprar.nao.informado", null, new Locale(requestService.getLocale())));
                }
            }

            empresaDao.getCurrentSession().clear();
            Empresa empresa = vitioAdapter.toEntity(configDTO, empresaDao.findById(configDTO.getEmpresa().getCodigo()));
            this.empresaService.update(empresa);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public void salvarConfiguracaoIntegracaoParceiroFidelidade(ConfiguracaoIntegracaoParceiroFidelidadeDTO configParceiroFidelidadeDTO) throws ServiceException {
        try {
            if (configParceiroFidelidadeDTO.isUsarParceiroFidelidade()) {
                if (Uteis.nullOrEmpty(configParceiroFidelidadeDTO.getParceiroFidelidade().getClientId())) {
                    throw new ServiceException(messageSource.getMessage("integracao.parceiro.fidelidade.clientid.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (Uteis.nullOrEmpty(configParceiroFidelidadeDTO.getParceiroFidelidade().getClientSecret())) {
                    throw new ServiceException(messageSource.getMessage("integracao.parceiro.fidelidade.clientsecret.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (Uteis.nullOrEmpty(configParceiroFidelidadeDTO.getParceiroFidelidade().getClientIdRedemption())) {
                    throw new ServiceException(messageSource.getMessage("integracao.parceiro.fidelidade.clientidredemption.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (Uteis.nullOrEmpty(configParceiroFidelidadeDTO.getParceiroFidelidade().getClientSecretRedemption())) {
                    throw new ServiceException(messageSource.getMessage("integracao.parceiro.fidelidade.clientsecretredemption.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (Uteis.nullOrEmpty(configParceiroFidelidadeDTO.getParceiroFidelidade().getCodigoLoja())) {
                    throw new ServiceException(messageSource.getMessage("integracao.parceiro.fidelidade.codigoloja.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (Uteis.nullOrEmpty(configParceiroFidelidadeDTO.getParceiroFidelidade().getCodigoMaquina())) {
                    throw new ServiceException(messageSource.getMessage("integracao.parceiro.fidelidade.codigomaquina.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (Uteis.nullOrEmpty(configParceiroFidelidadeDTO.getParceiroFidelidade().getCodigoOferta())) {
                    throw new ServiceException(messageSource.getMessage("integracao.parceiro.fidelidade.codigooferta.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (Uteis.nullOrEmpty(configParceiroFidelidadeDTO.getParceiroFidelidade().getCodigoResgate())) {
                    throw new ServiceException(messageSource.getMessage("integracao.parceiro.fidelidade.codigoresgate.nao.informado", null, new Locale(requestService.getLocale())));
                }
            }
            empresaDao.getCurrentSession().clear();
            Empresa empresa = empresaDao.findById(configParceiroFidelidadeDTO.getEmpresa().getCodigo());
            if (empresa != null) {
                parceiroFidelidadeService.saveOrUpdate(configParceiroFidelidadeDTO.getParceiroFidelidade());
                empresa.setUsarParceiroFidelidade(configParceiroFidelidadeDTO.isUsarParceiroFidelidade());
                this.empresaService.update(empresa);
            } else {
                throw new ServiceException(messageSource.getMessage("integracao.falha.salvar.integracao", null, new Locale(requestService.getLocale())));
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public void salvarConfiguracaoIntegracaoNotificacaoWebhook(ConfiguracaoIntegracaoNotificacaoWebhookDTO configIntegracaoNotificacaoWebhookDTO) throws ServiceException {
        try {
            if (configIntegracaoNotificacaoWebhookDTO.isNotificarWebhook()) {
                if (Uteis.nullOrEmpty(configIntegracaoNotificacaoWebhookDTO.getUrlWebhookNotificar())) {
                    throw new ServiceException(messageSource.getMessage("integracao.notificacao.web.hook.url.nao.informada", null, new Locale(requestService.getLocale())));
                }
            }

            empresaDao.getCurrentSession().clear();
            Empresa empresa = notificaoWebhookAdapter.toEntity(configIntegracaoNotificacaoWebhookDTO, empresaDao.findById(configIntegracaoNotificacaoWebhookDTO.getEmpresa().getCodigo()));
            if (empresa != null) {
                this.empresaService.update(empresa);
            } else {
                throw new ServiceException(messageSource.getMessage("integracao.falha.salvar.integracao", null, new Locale(requestService.getLocale())));
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public void salvarConfiguracaoIntegracaoAmigoFit(ConfiguracaoIntegracaoAmigoFitDTO configAmigoFitDTO) throws ServiceException {
        try {
            if (configAmigoFitDTO.isHabilitada()) {
                if (Uteis.nullOrEmpty(configAmigoFitDTO.getNomeUsuarioAmigoFit())) {
                    throw new ServiceException(messageSource.getMessage("integracao.amigo.fit.usuario.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (Uteis.nullOrEmpty(configAmigoFitDTO.getSenhaUsuarioAmigoFit())) {
                    throw new ServiceException(messageSource.getMessage("integracao.amigo.fit.senha.nao.informada", null, new Locale(requestService.getLocale())));
                }
            }

            empresaDao.getCurrentSession().clear();
            Empresa empresa = amigoFitAdapter.toEntity(configAmigoFitDTO, empresaDao.findById(configAmigoFitDTO.getEmpresa().getCodigo()));
            if (empresa != null) {
                this.empresaService.update(empresa);
            } else {
                throw new ServiceException(messageSource.getMessage("integracao.falha.salvar.integracao", null, new Locale(requestService.getLocale())));
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public void salvarConfiguracaoIntegracaoWeHelp(ConfiguracaoIntegracaoWeHelpDTO configWeHelp) throws ServiceException {
        try {
            empresaDao.getCurrentSession().clear();
            Empresa empresa = empresaDao.findById(configWeHelp.getEmpresa().getCodigo());
            empresa.setIntegracaoWeHelpHabilitada(configWeHelp.isHabilitada());
            empresa.setCpfCodigoInternoWeHelp(configWeHelp.isCpfCodigoInternoWeHelp());
            this.empresaService.update(empresa);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    public void salvarConfiguracaoIntegracaoF360(ConfiguracaoIntegracaoF360RelatorioDTO configF360) throws ServiceException {
        try {
            if (configF360.isHabilitada()) {
                if (Uteis.nullOrEmpty(configF360.getFtpServer())) {
                    throw new ServiceException(messageSource.getMessage("integracao.f360.ftpserver.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (Uteis.intNullOrEmpty(configF360.getFtpPort())) {
                    throw new ServiceException(messageSource.getMessage("integracao.f360.ftpporta.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (Uteis.nullOrEmpty(configF360.getUser())) {
                    throw new ServiceException(messageSource.getMessage("integracao.f360.user.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (Uteis.nullOrEmpty(configF360.getDir())) {
                    throw new ServiceException(messageSource.getMessage("integracao.f360.dir.nao.informado", null, new Locale(requestService.getLocale())));
                }
                if (!configF360.getDir().contains("/files")) {
                    throw new ServiceException(messageSource.getMessage("integracao.f360.dir.informado.sem.files", null, new Locale(requestService.getLocale())));
                }
            }
            this.empresaDao.getCurrentSession().clear();
            Empresa empresa = empresaDao.findById(configF360.getEmpresa().getCodigo());
            empresa.setIntegracaoF360RelFatHabilitada(configF360.isHabilitada());
            empresa.setIntegracaoF360FtpServer(configF360.getFtpServer().trim());
            empresa.setIntegracaoF360FtpPort(configF360.getFtpPort());
            empresa.setIntegracaoF360User(configF360.getUser().trim());
            empresa.setIntegracaoF360Password(null);
            empresa.setIntegracaoF360Dir(configF360.getDir().trim());
            empresa.setIntegracaoF360Quinzenal(configF360.isQuinzenal());
            empresaService.update(empresa);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    public void salvarConfiguracaoIntegracaoConciliadora(ConfiguracaoIntegracaoConciliadoraDTO configConciliadora) throws ServiceException {
        try {
            if (configConciliadora.isUsarConciliadora()) {
                if (Uteis.nullOrEmpty(configConciliadora.getEmpresaConciliadora())) {
                    throw new ServiceException(messageSource.getMessage("integracao.conciliadora.idempresa.nao.informada", null, new Locale(requestService.getLocale())));
                }
                if (Uteis.nullOrEmpty(configConciliadora.getSenhaConciliadora())) {
                    throw new ServiceException(messageSource.getMessage("integracao.concliadora.senha.nao.informada", null, new Locale(requestService.getLocale())));
                }
            }
            this.empresaDao.getCurrentSession().clear();
            Empresa empresa = empresaDao.findById(configConciliadora.getEmpresa().getCodigo());
            empresa.setUsarConciliadora(configConciliadora.isUsarConciliadora());
            empresa.setEmpresaConciliadora(configConciliadora.getEmpresaConciliadora());
            empresa.setSenhaConciliadora(configConciliadora.getSenhaConciliadora());
            empresaService.update(empresa);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    public void salvarConfiguracaoIntegracaoSescDf(ConfiguracaoIntegracaoSescDfDTO configSesc) throws ServiceException {
        try {
            if (configSesc.getUsarSescDf() && Uteis.nullOrEmpty(configSesc.getToken())) {
                throw new ServiceException(messageSource.getMessage("integracao.sistema.sesc.df.token.nao.inforamdo", null, new Locale(requestService.getLocale())));
            }

            this.empresaDao.getCurrentSession().clear();

            final Empresa empresa = empresaDao.findById(configSesc.getEmpresa().getCodigo());
            empresa.setUsarSescDf(configSesc.getUsarSescDf());
            empresa.setTokenSescDf(configSesc.getToken());
            empresaService.update(empresa);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public void processarConciliadora(FiltroProcessarConciliadoraJSON filtros) throws Exception {
        if (filtros.getDataInicial() == null) {
            throw new ServiceException(messageSource.getMessage("integracao.concliadora.datainicial.nao.informada", null, new Locale(requestService.getLocale())));
        }
        if (filtros.getDataFinal() == null) {
            throw new ServiceException(messageSource.getMessage("integracao.concliadora.datafinal.nao.informada", null, new Locale(requestService.getLocale())));
        }
        if (Uteis.intNullOrEmpty(filtros.getCodigoEmpresa())) {
            throw new ServiceException(messageSource.getMessage("integracao.concliadora.codigoempresa.nao.informado", null, new Locale(requestService.getLocale())));
        }

        StringBuilder urlZw = new StringBuilder(discoveryService.getClientDiscovery().getServiceUrls().getZwUrl());
        urlZw.append("/prest/conciliadora")
                .append("?key=").append(requestService.getUsuarioAtual().getChave())
                .append("&operacao=processar")
                .append("&codigoEmpresa=" + filtros.getCodigoEmpresa())
                .append("&codigoRecibo=" + filtros.getCodigoRecibo())
                .append("&dataInicial=" + Uteis.getData(filtros.getDataInicial(), "bd"))
                .append("&dataFinal=" + Uteis.getData(filtros.getDataInicial(), "bd"))
                .append("&reprocessar=true")
                .append("&manual=true");

        ResponseEntity<String> responseZw = httpServico.doJson(
                urlZw.toString(),
                null, HttpMethod.POST, "");

        JSONObject jsonObject = new JSONObject(responseZw);
        if (!jsonObject.optString("erro").isEmpty()) {
            String msg = "Falha ao tentar processar conciliadora! " + jsonObject.optString("erro");
            System.out.println(msg);
            throw new ServiceException(msg);
        }
    }

    @Override
    public void estornarReciboNaConciliadora(Integer codigoRecibo) throws Exception {
        if (Uteis.intNullOrEmpty(codigoRecibo)) {
            throw new ServiceException(messageSource.getMessage("integracao.concliadora.codigorecibo.nao.informado", null, new Locale(requestService.getLocale())));
        }

        StringBuilder urlZw = new StringBuilder(discoveryService.getClientDiscovery().getServiceUrls().getZwUrl());
        urlZw.append("/prest/conciliadora")
                .append("?key=").append(requestService.getUsuarioAtual().getChave())
                .append("&operacao=estornar")
                .append("&codigoRecibo=" + codigoRecibo);

        ResponseEntity<String> responseZw = httpServico.doJson(
                urlZw.toString(),
                null, HttpMethod.POST, "");

        JSONObject jsonObject = new JSONObject(responseZw);
        if (!jsonObject.optString("erro").isEmpty()) {
            String msg = "Falha ao tentar estornar recibo na conciliadora! " + jsonObject.optString("erro");
            System.out.println(msg);
            throw new ServiceException(msg);
        }
    }


    public void salvarConfiguracaoIntegracaoSms(ConfiguracaoIntegracaoSmsDTO configSmsDTO) throws ServiceException {
        try {
            this.empresaDao.getCurrentSession().clear();
            Empresa empresa = empresaDao.findById(configSmsDTO.getEmpresa().getCodigo());
            String tokenSMSMasc = Util.mascararDado(empresa.getTokenSMS(), 8);
            if (!UteisValidacao.emptyString(configSmsDTO.getTokenSMS()) &&
                    (tokenSMSMasc == null || !tokenSMSMasc.equalsIgnoreCase(configSmsDTO.getTokenSMS()))) {
                empresa.setTokenSMS(configSmsDTO.getTokenSMS());
            }
            String tokenSMSShortCodeMasc = Util.mascararDado(empresa.getTokenSMSShortCode(), 8);
            if (!UteisValidacao.emptyString(configSmsDTO.getTokenSMSShortCode()) &&
                    (tokenSMSShortCodeMasc == null || !tokenSMSShortCodeMasc.equalsIgnoreCase(configSmsDTO.getTokenSMSShortCode()))) {
                empresa.setTokenSMSShortCode(configSmsDTO.getTokenSMSShortCode());
            }
            empresaService.update(empresa);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }


    public ConfiguracaoIntegracaoSistemaContabilAlterDataDTO findConfiguracaoIntegracaoSistemaContabilAlterData() {
        ConfiguracaoIntegracaoSistemaContabilAlterDataDTO configDTO = new ConfiguracaoIntegracaoSistemaContabilAlterDataDTO();
        try (SessionImplementor sessionImplementor = empresaDao.createSessionCurrentWork()) {
            sessionImplementor.doWork(con -> {
                try {
                    ResultSet rs = con.createStatement().executeQuery("SELECT habilitarexportacaoalterdata FROM configuracaofinanceiro");
                    if (rs.next()) {
                        configDTO.setHabilitarExportacaoAlterData(rs.getBoolean("habilitarexportacaoalterdata"));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    con.close();
                }
            });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return configDTO;
    }

    public void salvarConfiguracaoIntegracaoSistemaContabilAlterData(ConfiguracaoIntegracaoSistemaContabilAlterDataDTO configDTO) {
        try (SessionImplementor sessionImplementor = empresaDao.createSessionCurrentWork()) {
            sessionImplementor.doWork(con -> {
                try {
                    con.createStatement().executeUpdate("update configuracaofinanceiro set habilitarexportacaoalterdata = " + configDTO.isHabilitarExportacaoAlterData());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    con.close();
                }
            });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void salvarConfiguracaoIntegracaoRecursosFacilitePay(ConfiguracaoIntegracaoRecursosFacilitePayDTO configDTO) throws ServiceException {
        try {
            empresaDao.getCurrentSession().clear();
            Empresa empresa = recursoFacilitePayAdapter.toEntity(configDTO, empresaDao.findById(configDTO.getEmpresa().getCodigo()));
            this.empresaService.update(empresa);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public void salvarConfiguracaoIntegracaoNuvemshop(ConfiguracaoIntegracaoNuvemshopDTO configDTO) throws ServiceException {
        try {
            empresaDao.getCurrentSession().clear();
            Empresa empresa = empresaDao.findById(configDTO.getEmpresa());

            empresa.setIntegracaoNuvemshopHabilitada(configDTO.isHabilitada());

            if (configDTO.isEmpresaFranqueadora()) {
                this.empresaService.update(empresa);

            } else {
                //empresas franqueadas precisam fornecer o email pra que seja possivel identificar se o pedido feito é da loja no qual o webhook foi disparado no servlet do zw
                empresa.setIntegracaoNuvemshopEmail(configDTO.getEmail());
                // Empresa franqueada tem o webhook registrado aqui;
                boolean sucesso = registrarWebhookNuvemshopEmEmpresaFranqueada(configDTO.getStoreId(), configDTO.getAccessToken(), empresa.getCodigo());

                if (sucesso) {
                    empresa.setIntegracaoNuvemshopTokenAcesso(configDTO.getAccessToken());
                    empresa.setIntegracaoNuvemshopStoreId(configDTO.getStoreId());
                    this.empresaService.update(empresa);
                } else {
                    throw new ServiceException("Falha ao registrar o webhook. A empresa não foi atualizada.");
                }
            }

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }


    public boolean registrarWebhookNuvemshopEmEmpresaFranqueada(String storeId, String accessToken, int codigoEmpresa) throws Exception {
        String URL_NUVEMSHOP_API = "https://api.nuvemshop.com.br/v1/" + storeId + "/webhooks";

        String chave = requestService.getUsuarioAtual().getChave();

        String endpointParaSerDisparadoNoEvento = discoveryService.getClientDiscovery().getServiceUrls().getApiZwUrl() + "/integracao-nuvemshop/consultarPedidosJson" + "?empresa=" + codigoEmpresa + "&chave=" + chave;

        HttpURLConnection connection = null;

        try {
            URL url = new URL(URL_NUVEMSHOP_API);
            connection = (HttpURLConnection) url.openConnection();
            connection.setDoOutput(true);
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Authentication", "bearer " + accessToken);

            JSONObject requestBody = new JSONObject();
            requestBody.put("event", "order/updated");
            requestBody.put("url", endpointParaSerDisparadoNoEvento);
            // requestBody.put("url", ENDPOINT_ZW_API_REGISTRADO_WEBHOOK);


            OutputStream os = connection.getOutputStream();
            os.write(requestBody.toString().getBytes("UTF-8"));
            os.close();

            int responseCode = connection.getResponseCode();
            boolean webhookRegistradoSucesso = responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK;

            if (webhookRegistradoSucesso) {
                return true;
            } else {
                BufferedReader br = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = br.readLine()) != null) {
                    response.append(line);
                }
                br.close();
                JSONObject jsonObject = new JSONObject(response.toString());
                String message = jsonObject.optString("message", "Cod. Erro");
                String description = jsonObject.optString("description", "Erro desconhecido");
                throw new RuntimeException("Falha ao registrar webhook: " + message + " - " + description);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    @Override
    public void salvarConfiguracaoIntegracaoManyChat(ConfiguracaoIntegracaoManyChatDTO configDTO) throws ServiceException {
        try {
            empresaDao.getCurrentSession().clear();
            Empresa empresa = empresaDao.findById(configDTO.getEmpresa().getCodigo());
            empresa.setIntegracaoManyChatHabilitada(configDTO.getHabilitada());
            empresa.setIntegracaoManyChatTokenApi(configDTO.getToken());
            if (configDTO.getTagUnidade() != null) {
                String tagUnidade = String.format("%s;%s", configDTO.getTagUnidade().getId(), configDTO.getTagUnidade().getName());
                empresa.setIntegracaoManyChatTagUnidade(tagUnidade);
            } else {
                empresa.setIntegracaoManyChatTagUnidade(null);
            }
            this.empresaService.update(empresa);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public List<TagDTO> consultarTagsManyChat(Integer codEmpresa) throws ServiceException {
        try {
            empresaDao.getCurrentSession().clear();
            Empresa empresa = empresaDao.findById(codEmpresa);
            if (empresa == null) {
                throw new ServiceException("Empresa não encontrada");
            }
            if (!empresa.getIntegracaoManyChatHabilitada()) {
                throw new ServiceException("Integração ManyChat não habilitada");
            }
            if (UteisValidacao.emptyString(empresa.getIntegracaoManyChatTokenApi())) {
                throw new ServiceException("Token ManyChat não informado");
            }

            ResponseEntity<String> response = httpServico.doJson("https://api.manychat.com/fb/page/getTags", null,
                    HttpMethod.GET, "Bearer " + empresa.getIntegracaoManyChatTokenApi());
            JSONObject jsonResponsae = new JSONObject(response.getBody());
            if (!jsonResponsae.optString("status").equals("success")) {
                throw new ServiceException("Falha ao obter tags do ManyChat");
            }

            JSONArray jsonArray = jsonResponsae.getJSONArray("data");
            List<TagDTO> tags = new ArrayList<>();
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                TagDTO tag = new TagDTO();
                tag.setId(jsonObject.getInt("id"));
                tag.setName(jsonObject.getString("name"));
                tags.add(tag);
            }
            return tags;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

}
