package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoEnvioAcessoPratiqueDTO;
import com.pacto.config.exceptions.ServiceException;

public interface ConfiguracaoIntegracaoEnvioAcessoPratiqueService {

    ConfiguracaoIntegracaoEnvioAcessoPratiqueDTO findByEmpresaId(Integer codigoEmpresa) throws ServiceException;

    ConfiguracaoIntegracaoEnvioAcessoPratiqueDTO salvar(ConfiguracaoIntegracaoEnvioAcessoPratiqueDTO configuracaoIntegracaoEnvioAcessoPratiqueDTO) throws ServiceException;

}
