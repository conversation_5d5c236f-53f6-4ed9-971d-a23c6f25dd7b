package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.MovParcelaAdapter;
import com.pacto.adm.core.dao.interfaces.*;
import com.pacto.adm.core.dto.MovParcelaDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.dto.filtros.FiltroMovParcelaJSON;
import com.pacto.adm.core.entities.*;
import com.pacto.adm.core.enumerador.TipoObservacaoOperacaoEnum;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.adm.core.services.interfaces.ClienteMensagemService;
import com.pacto.adm.core.services.interfaces.LogService;
import com.pacto.adm.core.services.interfaces.MovParcelaService;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.Uteis;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;


@Service
public class MovParcelaServiceImpl implements MovParcelaService {

    @Autowired
    private MovParcelaDao movParcelaDao;
    @Autowired
    private MovProdutoParcelaDao movProdutoParcelaDao;
    @Autowired
    private MovProdutoDao movProdutoDao;
    @Autowired
    private ClienteMensagemService clienteMensagemService;
    @Autowired
    private MovParcelaAdapter movParcelaAdapter;
    @Autowired
    private LogService logService;
    @Autowired
    private ObservacaoOperacaoDao observacaoOperacaoDao;
    @Autowired
    private RequestService requestService;
    @Autowired
    private UsuarioDao usuarioDao;

    public List<MovParcelaDTO> findAllByCodPessoa(Integer codPessoa, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<MovParcela> movParcelas = movParcelaDao.findAllByPessoa(codPessoa);

            List<MovParcelaDTO> movParcelaDTOS = movParcelaAdapter.toDtos(movParcelas);

            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos((long) movParcelas.size());

                if (paginadorDTO.getPage() != null && paginadorDTO.getSize() != null) {
                    int primeiroPaginacao = (int) (paginadorDTO.getPage() * paginadorDTO.getSize());
                    int ultimoRegistro = (int) ((paginadorDTO.getPage() * paginadorDTO.getSize()) + paginadorDTO.getSize());
                    if (ultimoRegistro > movParcelaDTOS.size()) {
                        movParcelaDTOS = movParcelaDTOS.subList(primeiroPaginacao, movParcelaDTOS.size());
                    } else {
                        movParcelaDTOS = movParcelaDTOS.subList(primeiroPaginacao, ultimoRegistro);
                    }
                }
            }

            return movParcelaDTOS;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<MovParcelaDTO> findAllByCodContrato(Integer codContrato, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<MovParcela> movParcelas = movParcelaDao.fidAllByCodigoContrato(codContrato, paginadorDTO);
            return movParcelaAdapter.toDtos(movParcelas);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    public List<MovParcelaDTO> findAllByCodRecibo(Integer codRecibo) throws ServiceException {
        try {
            List<MovProdutoParcela> movProdutoParcelas = movProdutoParcelaDao.findAllByRecibo(codRecibo);

            Map<Integer, MovParcela> mapaParcelasRecibo = new HashMap<>();
            for (MovProdutoParcela mpp : movProdutoParcelas) {
                MovParcela mpar = mapaParcelasRecibo.get(mpp.getMovParcela().getCodigo());
                if (mpar == null) {
                    mpar = mpp.getMovParcela();
                    mapaParcelasRecibo.put(mpar.getCodigo(), mpar);
                }
            }

            return movParcelaAdapter.toDtos(new ArrayList<>(mapaParcelasRecibo.values()));
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<MovParcelaDTO> consultarPorCodigoPessoa(Integer codPessoa, Integer matricula, FiltroMovParcelaJSON filtros, PaginadorDTO paginadorDTO) throws Exception {
        List<MovParcelaDTO> movParcelaDTOS = movParcelaAdapter.toDtos(movParcelaDao.consultarPorCodigoPessoa(codPessoa, matricula, filtros, paginadorDTO));
        return movParcelaDTOS;
    }

    @Override
    public void cancelarParcelaSorteio(Integer codParcela, String justificativa) throws Exception {
        MovParcela movParcela = movParcelaDao.findById(codParcela);
        MovParcela movParcelaAnterior = movParcela.clone();

        cancelarProdutosVinculados(movParcela.getCodigo());

        movParcela.setSituacao("CA");
        movParcelaDao.update(movParcela);
        clienteMensagemService.excluirClienteMensagemPorMovParcela(movParcela.getCodigo());

        registrarJustificativaParcelaCancelada(movParcela, justificativa);

        logService.incluirLogInclusaoAlteracao(movParcela, movParcelaAnterior, "CANCELAMENTO PARCELA", "Conta corrente");
    }

    @Override
    public List<MovParcelaDTO> findAllByCodProduto(Integer codMovProduto, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<MovProdutoParcela> movProdutoParcelas = movProdutoParcelaDao.findByMovProduto(codMovProduto, paginadorDTO);
            List<MovParcela> parcelas = new ArrayList<>();
            for (MovProdutoParcela mpp : movProdutoParcelas) {
                parcelas.add(mpp.getMovParcela());
            }

            return movParcelaAdapter.toDtos(parcelas);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void cancelarProdutosVinculados(Integer codParcela) throws Exception {
        HashMap<Integer, MovProduto> produtos = new HashMap<>();
        MovParcela parcela = movParcelaDao.findById(codParcela);
        Iterator i = parcela.getMovProdutosParcelas().iterator();
        while (i.hasNext()) {
            MovProdutoParcela mpp = (MovProdutoParcela) i.next();
            if (produtos.containsKey(mpp.getMovProduto())) {
                produtos.get(mpp.getMovProduto()).getMovProdutosParcelas().add(mpp);
            } else {
                MovProduto produto = movProdutoDao.findById(mpp.getMovProduto().getCodigo());
                produto.getMovProdutosParcelas().add(mpp);
                produtos.put(produto.getCodigo(), produto);
            }
        }
        Set<Integer> chaves = produtos.keySet();
        Double soma = 0.0;
        for (Integer codigo : chaves) {
            soma = 0.0;
            Iterator p = produtos.get(codigo).getMovProdutosParcelas().iterator();
            while (p.hasNext()) {
                MovProdutoParcela mpp = (MovProdutoParcela) p.next();
                soma = Uteis.arredondarForcando2CasasDecimais(soma + mpp.getValorPago().doubleValue());
            }
            if (Uteis.arredondarForcando2CasasDecimais(produtos.get(codigo).getTotalFinal().doubleValue()) <= soma) {
                produtos.get(codigo).setSituacao("CA");
                movProdutoDao.update(produtos.get(codigo));
            } else {
                MovProduto produtoCan = (MovProduto) produtos.get(codigo).clone();
                produtoCan.setCodigo(null);
                produtoCan.setTotalFinal(BigDecimal.valueOf(soma));
                produtoCan.setPrecoUnitario(BigDecimal.valueOf(soma));
                produtoCan.setValorDesconto(BigDecimal.valueOf(0.0));
                produtoCan.setSituacao("CA");
                movProdutoDao.save(produtoCan);
                Iterator pc = produtos.get(codigo).getMovProdutosParcelas().iterator();
                while (pc.hasNext()) {
                    MovProdutoParcela mpp = (MovProdutoParcela) pc.next();
                    mpp.setMovProduto(produtoCan);
                    movProdutoParcelaDao.update(mpp);
                }
                Double totalFinal = produtos.get(codigo).getTotalFinal().doubleValue() - soma;
                produtos.get(codigo).setTotalFinal(BigDecimal.valueOf(totalFinal));
                Double precoUnitario = produtos.get(codigo).getTotalFinal().doubleValue() + produtos.get(codigo).getValorDesconto().doubleValue();
                produtos.get(codigo).setPrecoUnitario(BigDecimal.valueOf(precoUnitario));

                Set<MovProdutoParcela> movProdutoParcelas = new HashSet<>();
                movProdutoParcelas.addAll(movProdutoParcelaDao.consultarPorCodigoMovProdutos(produtos.get(codigo).getCodigo()));
                produtos.get(codigo).setMovProdutosParcelas(movProdutoParcelas);
                Iterator pa = produtos.get(codigo).getMovProdutosParcelas().iterator();
                boolean produtoPago = true;
                while (pa.hasNext()) {
                    MovProdutoParcela mpp = (MovProdutoParcela) pa.next();
                    if (mpp.getReciboPagamento().getCodigo() == 0) {
                        produtoPago = false;
                        produtos.get(codigo).setSituacao("EA");
                        break;
                    }
                }
                if (produtoPago) {
                    produtos.get(codigo).setSituacao("PG");
                }
                movProdutoDao.update(produtos.get(codigo));
            }
        }
    }

    private void registrarJustificativaParcelaCancelada(MovParcela movParcela, String justificativa) throws Exception {
        ObservacaoOperacao observacaoOperacao = new ObservacaoOperacao();
        observacaoOperacao.setMovParcela(movParcela);
        observacaoOperacao.setJustificativa(justificativa);
        observacaoOperacao.setTipoOperacao(TipoObservacaoOperacaoEnum.PARCELA_CANCELADA.getTipo());
        observacaoOperacao.setValor(Uteis.arredondarForcando2CasasDecimais(movParcela.getValorParcela().doubleValue()));
        observacaoOperacao.setDataOperacao(Calendario.hoje());
        Usuario usuario = usuarioDao.findById(requestService.getUsuarioAtual().getCodZw());
        observacaoOperacao.setUsuarioResponsavel(usuario.getNome());
        observacaoOperacaoDao.save(observacaoOperacao);
    }
}
