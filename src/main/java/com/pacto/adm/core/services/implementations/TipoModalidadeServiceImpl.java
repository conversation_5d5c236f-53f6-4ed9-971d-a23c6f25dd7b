package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.TipoModalidadeAdapter;
import com.pacto.adm.core.dao.interfaces.TipoModalidadeDao;
import com.pacto.adm.core.dto.LogDTO;
import com.pacto.adm.core.dto.TipoModalidadeDTO;
import com.pacto.adm.core.dto.filtros.FiltroLogClienteJSON;
import com.pacto.adm.core.dto.filtros.FiltroTipoModalidadeJSON;
import com.pacto.adm.core.entities.contrato.TipoModalidade;
import com.pacto.adm.core.services.interfaces.LogService;
import com.pacto.adm.core.services.interfaces.TipoModalidadeService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.hibernate.exception.ConstraintViolationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class TipoModalidadeServiceImpl implements TipoModalidadeService {

    @Autowired
    private TipoModalidadeDao tipoModalidadeDao;
    @Autowired
    private TipoModalidadeAdapter tipoModalidadeAdapter;
    @Autowired
    private LogService logService;


    @Override
    public TipoModalidade findById(Integer id) throws Exception {
        TipoModalidade obj = tipoModalidadeDao.findById(id);
        return obj;
    }

    @Override
    public List<TipoModalidadeDTO> findAll(FiltroTipoModalidadeJSON filtroTipoModalidadeJSON, PaginadorDTO paginadorDTO) throws ServiceException  {
        try {
            return tipoModalidadeAdapter.toDtos(tipoModalidadeDao.findAll(filtroTipoModalidadeJSON, paginadorDTO));
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public TipoModalidadeDTO saveOrUpdate(TipoModalidadeDTO tipoModalidadeDTO) throws Exception {
        try {
            tipoModalidadeDao.getCurrentSession().clear();
            TipoModalidade tipoModalidade;

            TipoModalidade tipoModalidadeAnterior = new TipoModalidade();
            if(Uteis.intNullOrEmpty(tipoModalidadeDTO.getCodigo())) {
                tipoModalidade = tipoModalidadeAdapter.toEntity(tipoModalidadeDTO);
                tipoModalidade = tipoModalidadeDao.save(tipoModalidade);
            } else {
                tipoModalidade = tipoModalidadeDao.findById(tipoModalidadeDTO.getCodigo());
                tipoModalidadeAnterior = tipoModalidade.clone();
                tipoModalidade = tipoModalidadeAdapter.toEntity(tipoModalidadeDTO);
                tipoModalidade = tipoModalidadeDao.update(tipoModalidade);
            }

            TipoModalidadeDTO dtoRetornar = tipoModalidadeAdapter.toDto(tipoModalidade);
            logService.incluirLogInclusaoAlteracao(tipoModalidade, tipoModalidadeAnterior, "TIPOMODALIDADE", "Tipo Modalidade");

            return dtoRetornar;
        } catch (Exception e) {
            if (e.getCause() instanceof ConstraintViolationException) {
                org.hibernate.exception.ConstraintViolationException constraintViolationException = ((org.hibernate.exception.ConstraintViolationException) e.getCause());
                throw new ServiceException(constraintViolationException.getSQLException());
            }
            throw new ServiceException(e);
        }
    }

    @Override
    public void delete(Integer id) throws ServiceException {
        try {
            tipoModalidadeDao.getCurrentSession().clear();
            TipoModalidade tipoModalidade = tipoModalidadeDao.findById(id);
            tipoModalidadeDao.delete(tipoModalidade);
            logService.incluirLogExcluscao(tipoModalidade, "TIPOMODALIDADE", "Tipo Modalidade");
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public List<LogDTO> buscarLogs(FiltroLogClienteJSON filtros, Integer idTipoMod, PaginadorDTO paginadorDTO) throws Exception {
        try {
            String idTipoModalidade = new String();
            if(idTipoMod != null) {
                idTipoModalidade = idTipoMod.toString();
            }
            List<LogDTO> logs = logService.consultarPorEntidade("TIPOMODALIDADE", filtros, idTipoModalidade, paginadorDTO);
            return logs;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }

    }

}
