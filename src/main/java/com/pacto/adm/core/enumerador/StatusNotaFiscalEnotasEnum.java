package com.pacto.adm.core.enumerador;

public enum StatusNotaFiscalEnotasEnum {

    NENHUM(0, "", "", "", true, "statusNenhum", false, ""),
    GERADA(1, "Gerada", "Gerada", "Gerada pelo sistema aguardando envio para eNotas.", true, "statusGerada", false, "Gerada pelo sistema aguardando envio para eNotas."),
    AGUARDANDOAUTORIZACAO(2, "Aguardando Autorizacao", "AguardandoAutorizacao", "Status inicial da nota fiscal.", true, "statusAguardando", false, "Status inicial da nota fiscal."),
    SOLICITANDOAUTORIZACAO(3, "Solicitando Autorizacao", "SolicitandoAutorizacao", "Nota fiscal sendo incluída na fila de transmissão à prefeitura.", true, "statusAguardando", false, "Nota fiscal sendo incluída na fila de transmissão do SEFAZ."),
    AUTORIZACAOSOLICITADA(4, "Autorizacao Solicitada", "AutorizacaoSolicitada", "Nota fiscal incluída na fila de transmissão à prefeitura.", true, "statusAguardando", false, "Nota fiscal incluída na fila de transmissão do SEFAZ."),
    EMPROCESSODEAUTORIZACAO(5, "Processo De Autorizacao", "EmProcessoDeAutorizacao", "Sua nota fiscal foi transmitida à prefeitura e o Gateway está aguardando um retorno.", true, "statusAguardando", false, "Sua nota fiscal foi transmitida ao SEFAZ e o Gateway está aguardando um retorno."),
    AUTORIZADAAGUARDANDOGERACAOPDF(6, "Aguardando Geracao PDF", "AutorizadaAguardandoGeracaoPDF", "Sua nota foi autorizada pela prefeitura e está na fila de geração de PDF.", true, "statusAguardando", false, "Sua nota foi autorizada pelo SEFAZ e está na fila de geração de PDF."),
    AUTORIZADA(7, "Autorizado", "Autorizada", "Nota fiscal autorizada e com PDF pronto para download.", true, "statusAutorizada", false, "Nota fiscal autorizada e com PDF pronto para download."),
    NEGADA(8, "Negada", "Negada", "A prefeitura rejeitou sua nota fiscal.", true, "statusNegada", true, "O SEFAZ rejeitou sua nota fiscal."),
    SOLICITANDOCANCELAMENTO(9, "Solicitando Cancelamento", "SolicitandoCancelamento", "Nota fiscal sendo incluída na fila de cancelamento junto à prefeitura.", true, "statusCancelando", false, "Nota fiscal sendo incluída na fila de cancelamento junto ao SEFAZ."),
    CANCELAMENTOSOLICITADO(10, "Cancelamento Solicitado", "CancelamentoSolicitado", "Nota fiscal incluída na fila de cancelamento junto à prefeitura.", true, "statusCancelando", false, "Nota fiscal incluída na fila de cancelamento junto ao SEFAZ."),
    EMPROCESSODECANCELAMENTO(11, "Processo De Cancelamento", "EmProcessoDeCancelamento", "Cancelamento solicitado junto à prefeitura e o Gateway está aguardando um retorno.", true, "statusCancelando", false, "Cancelamento solicitado junto ao SEFAZ e o Gateway está aguardando um retorno."),
    CANCELADAAGUARDANDOATUALIZACAOPDF(12, "Cancelada Atualizando PDF", "CanceladaAguardandoAtualizacaoPDF", "Nota fiscal cancelada pela prefeitura e está na fila para atualização de seu PDF.", true, "statusCancelando", false, "Nota fiscal cancelada pelo SEFAZ e está na fila para atualização de seu PDF."),
    CANCELADA(13, "Cancelada", "Cancelada", "Nota fiscal cancelada e com PDF atualizado pronto para download.", true, "statusCancelada", true, "Nota fiscal cancelada e com PDF atualizado pronto para download."),
    CANCELAMENTONEGADO(14, "Cancelamento Negado", "CancelamentoNegado", "Prefeitura rejeitou o cancelamento de sua nota fiscal.", true, "statusCancelamentoNegado", false, "SEFAZ rejeitou o cancelamento de sua nota fiscal."),
    ERRO(15, "Erro na Geração", "Erro", "Erro na geração.", true, "statusErro", true, "Erro na geração."),
    INUTILIZACAOSOLICITADO(16, "Inutilização Solicitada", "InutilizacaoSolicitado", "Solitado inutilização.", true, "statusErro", false, "Inutilização Solicitada"),
    INUTILIZADA(17, "Inutilizada", "Inutilizada", "Número inutilizado.", true, "statusErro", true, "Número inutilizado."),
    INUTILIZACAONEGADA(17, "Inutilizacao Negada", "InutilizacaoNegada", "Número inutilizado.", true, "statusErro", false, "Número inutilizado."),
    REENVIADA(18, "Reenviada", "Reenviada", "Nota reenviada.", true, "statusErro", false, "Nota reenviada."),
    ;

    private Integer codigo;
    private String descricaoApresentar;
    private String descricaoEnotas;
    private String hint;
    private boolean apresentar;
    private String css;
    private boolean podeEstornar;
    private String hintNFC;

    StatusNotaFiscalEnotasEnum(Integer codigo, String descricaoApresentar, String descricaoEnotas, String hint, boolean apresentar, String css, boolean podeEstornar, String hintNFC) {
        this.codigo = codigo;
        this.descricaoApresentar = descricaoApresentar;
        this.descricaoEnotas = descricaoEnotas;
        this.hint = hint;
        this.apresentar = apresentar;
        this.css = css;
        this.podeEstornar = podeEstornar;
        this.hintNFC = hintNFC;
    }

    public static StatusNotaFiscalEnotasEnum obterPorCodigo(Integer codigo) {
        for (StatusNotaFiscalEnotasEnum obj : StatusNotaFiscalEnotasEnum.values()) {
            if (obj.getCodigo().equals(codigo))
                return obj;
        }
        return NENHUM;
    }

    public static StatusNotaFiscalEnotasEnum obterPorDescricaoEnotas(String descricaoEnotas) {
        for (StatusNotaFiscalEnotasEnum obj : StatusNotaFiscalEnotasEnum.values()) {
            if (obj.getDescricaoEnotas().toUpperCase().equals(descricaoEnotas.toUpperCase())) {
                return obj;
            }
        }
        return NENHUM;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getHint() {
        return hint;
    }

    public boolean isApresentar() {
        return apresentar;
    }

    public String getDescricaoApresentar() {
        return descricaoApresentar;
    }

    public String getDescricaoEnotas() {
        return descricaoEnotas;
    }

    public String getCss() {
        return css;
    }

    public boolean isPodeEstornar() {
        return podeEstornar;
    }

    public String getHintNFC() {
        return hintNFC;
    }
}
