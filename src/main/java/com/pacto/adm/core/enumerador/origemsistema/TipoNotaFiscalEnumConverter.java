package com.pacto.adm.core.enumerador.origemsistema;

import com.pacto.adm.core.enumerador.TipoNotaFiscalEnum;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter
public class TipoNotaFiscalEnumConverter implements AttributeConverter<TipoNotaFiscalEnum, Integer> {
    @Override
    public Integer convertToDatabaseColumn(TipoNotaFiscalEnum tipoNotaFiscalEnum) {
        if (tipoNotaFiscalEnum == null) {
            return null;
        }
        return tipoNotaFiscalEnum.getCodigo();
    }

    @Override
    public TipoNotaFiscalEnum convertToEntityAttribute(Integer db) {
        return TipoNotaFiscalEnum.obterPorCodigo(db);
    }
}