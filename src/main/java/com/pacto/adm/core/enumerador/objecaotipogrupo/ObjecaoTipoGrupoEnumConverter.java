package com.pacto.adm.core.enumerador.objecaotipogrupo;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter
public class ObjecaoTipoGrupoEnumConverter implements AttributeConverter<ObjecaoTipoGrupoEnum, String> {
    @Override
    public String convertToDatabaseColumn(ObjecaoTipoGrupoEnum origemSistemaEnum) {
        if (origemSistemaEnum == null) {
            return null;
        }
        return origemSistemaEnum.getCodigo();
    }

    @Override
    public ObjecaoTipoGrupoEnum convertToEntityAttribute(String db) {
        return ObjecaoTipoGrupoEnum.getByCodigo(db);
    }
}