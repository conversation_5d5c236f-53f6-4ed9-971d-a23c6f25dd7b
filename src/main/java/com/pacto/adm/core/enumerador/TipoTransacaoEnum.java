package com.pacto.adm.core.enumerador;

public enum TipoTransacaoEnum {

    NENHUMA(0, "Nenhuma"),
    AprovaFacilCB(1, "Aprova Fácil - CobreBem"),
    VINDI(2, "Vindi"),
    CIELO_ONLINE(3, "<PERSON>iel<PERSON>"),
    CIELO_DEBITO_ONLINE(5, "Cielo Débito Online"),
    MAXIPAGO(6, "MaxiPago"),
    E_REDE(7, "Rede"),
    E_REDE_DEBITO(8, "Rede Débito Online"),
    FITNESS_CARD(9, "Fitness Card"),
    GETNET_ONLINE(10, "Getnet"),
    STONE_ONLINE(11, "Stone"),
    MUNDIPAGG(12, "Mundipagg"),
    PAGAR_ME(13, "Pagar.me"),
    PACTO_PAY(14, "PactoPay"),
    STRIPE(15, "Stripe"),
    PAGOLIVRE(16, "PagoLivre"),
    VALORIBANK(17, "ValoriBank"),
    ONE_PAYMENT(18, "OnePayment"),
    ;

    private int id;
    private String descricao;

    TipoTransacaoEnum(int id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public int getId() {
        return id;
    }

    public static TipoTransacaoEnum getTipoTransacaoEnum(final int codigo) {
        for (TipoTransacaoEnum situacao : TipoTransacaoEnum.values()) {
            if (situacao.getId() == codigo) {
                return situacao;
            }
        }
        return TipoTransacaoEnum.NENHUMA;
    }

}
