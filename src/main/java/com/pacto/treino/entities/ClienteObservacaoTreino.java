package com.pacto.treino.entities;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Temporal;
import java.util.Date;

@Entity(name = "clienteobservacao")
public class ClienteObservacaoTreino {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Column(name = "cliente_codigo")
    private Integer cliente;
    private Integer usuario_codigo;
    @Column(columnDefinition = "boolean DEFAULT false")
    private Boolean importante = Boolean.FALSE;
    @Column(columnDefinition = "text", length = 9999)
    private String observacao;
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    private Date dataObservacao;
    private Boolean avaliacaoFisica = Boolean.FALSE;
    private String anexoKey;
    @Column(columnDefinition = "text")
    private String nomeArquivo;
    @Column(columnDefinition = "text")
    private String formatoArquivo;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Integer getUsuario_codigo() {
        return usuario_codigo;
    }

    public void setUsuario_codigo(Integer usuario_codigo) {
        this.usuario_codigo = usuario_codigo;
    }

    public Boolean getImportante() {
        return importante;
    }

    public void setImportante(Boolean importante) {
        this.importante = importante;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Date getDataObservacao() {
        return dataObservacao;
    }

    public void setDataObservacao(Date dataObservacao) {
        this.dataObservacao = dataObservacao;
    }

    public Boolean getAvaliacaoFisica() {
        return avaliacaoFisica;
    }

    public void setAvaliacaoFisica(Boolean avaliacaoFisica) {
        this.avaliacaoFisica = avaliacaoFisica;
    }

    public String getAnexoKey() {
        return anexoKey;
    }

    public void setAnexoKey(String anexoKey) {
        this.anexoKey = anexoKey;
    }

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }

    public String getFormatoArquivo() {
        return formatoArquivo;
    }

    public void setFormatoArquivo(String formatoArquivo) {
        this.formatoArquivo = formatoArquivo;
    }
}
